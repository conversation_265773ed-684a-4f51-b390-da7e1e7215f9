'use client'

import { useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';

// 熱力圖數據點接口
export interface HeatmapDataPoint {
  lat: number;
  lng: number;
  intensity: number; // 交易次數或其他強度值
}

interface HeatmapLayerProps {
  data: HeatmapDataPoint[];
  options?: {
    radius?: number;
    blur?: number;
    maxZoom?: number;
    max?: number;
    minOpacity?: number;
    gradient?: { [key: string]: string };
  };
  isVisible?: boolean;
}

export function HeatmapLayer({ 
  data, 
  options = {}, 
  isVisible = true 
}: HeatmapLayerProps) {
  const map = useMap();
  const heatLayerRef = useRef<any>(null);

  useEffect(() => {
    if (!map || !isVisible) {
      // 如果熱力圖不可見，移除圖層
      if (heatLayerRef.current) {
        map.removeLayer(heatLayerRef.current);
        heatLayerRef.current = null;
      }
      return;
    }

    // 動態導入 leaflet.heat
    const loadHeatmap = async () => {
      try {
        // 導入 leaflet.heat
        const L = await import('leaflet');
        await import('leaflet.heat');
        
        // 移除舊的熱力圖層
        if (heatLayerRef.current) {
          map.removeLayer(heatLayerRef.current);
        }

        // 轉換數據格式為 [lat, lng, intensity]
        const heatData = data.map(point => [
          point.lat,
          point.lng,
          point.intensity
        ]);

        // 默認配置
        const defaultOptions = {
          radius: 25,
          blur: 15,
          maxZoom: 17,
          max: 1.0,
          minOpacity: 0.4,
          gradient: {
            0.0: '#3388ff',
            0.2: '#00ff88',
            0.4: '#ffff00',
            0.6: '#ff8800',
            0.8: '#ff4400',
            1.0: '#ff0000'
          }
        };

        const finalOptions = { ...defaultOptions, ...options };

        // 創建熱力圖層
        heatLayerRef.current = (L.default as any).heatLayer(heatData, finalOptions);
        
        // 添加到地圖
        heatLayerRef.current.addTo(map);

      } catch (error) {
        console.error('Failed to load heatmap:', error);
      }
    };

    if (data.length > 0) {
      loadHeatmap();
    }

    // 清理函數
    return () => {
      if (heatLayerRef.current) {
        map.removeLayer(heatLayerRef.current);
        heatLayerRef.current = null;
      }
    };
  }, [map, data, options, isVisible]);

  return null; // 這個組件不渲染任何 DOM 元素
}
