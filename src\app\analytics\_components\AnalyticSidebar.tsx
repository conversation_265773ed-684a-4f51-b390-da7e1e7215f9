'use client';
import React, { createElement } from 'react';
import { type LucideIcon } from 'lucide-react';

// shadcn components
import {
	SidebarMenu,
	SidebarGroup,
	SidebarContent,
	SidebarMenuItem,
	SidebarMenuButton,
	SidebarGroupLabel,
	SidebarGroupContent,
} from '@/components/ui/sidebar';

// Typescripts
interface SidebarContentProps {
	data: {
		icon: LucideIcon | null;
		title: string;
		onClick: () => void;
		groupLabel: string;
		groupLabelClassName?: string;
		sidebarMenuLabelClassName?: string;
		[key: string]: unknown;
	}[];
}

const AnalyticSidebar: React.FC<SidebarContentProps> = ({ data }) => (
	<SidebarContent>
		{data.map(({ icon, title, onClick, groupLabel, groupLabelClassName, sidebarMenuLabelClassName }) => (
			<SidebarGroup key={groupLabel}>
				<SidebarGroupLabel className={groupLabelClassName}>{groupLabel}</SidebarGroupLabel>
				<SidebarGroupContent>
					<SidebarMenu>
						<SidebarMenu>
							<SidebarMenuItem key={title}>
								<SidebarMenuButton asChild size="sm" onClick={onClick} className="cursor-pointer">
									<div className="flex items-center gap-2">
										{icon && createElement(icon)}
										<span className={sidebarMenuLabelClassName}>{title}</span>
									</div>
								</SidebarMenuButton>
							</SidebarMenuItem>
						</SidebarMenu>
					</SidebarMenu>
				</SidebarGroupContent>
			</SidebarGroup>
		))}
	</SidebarContent>
);

export default AnalyticSidebar;
