"use client";
import { useEffect } from "react";
import {
  ColumnDef,
  flexRender,
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  type Table as TableType,
} from "@tanstack/react-table";

// shadcn ui
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectItem,
  SelectValue,
  SelectTrigger,
  SelectContent,
} from "@/components/ui/select";

// lucide icons
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  ArrowLeftToLineIcon,
  ArrowRightToLineIcon,
} from "lucide-react";

// Typescript
interface AnalyticTableProps<TData, TValue> {
  data: TData[];
  columns: ColumnDef<TData, TValue>[];
  onTableReady: (table: any) => void;
  isShowPagination?: boolean;
}

const getPaginationPages = ({
  table,
  totalPages,
  currentPageIndex,
}: {
  table: TableType<any>;
  totalPages: number;
  currentPageIndex: number;
}) => {
  const pages: (number | string)[] = [];
  let siblingCount = 1;
  const maxVisible = 5;

  if (totalPages <= 7) {
    // show all page index if less 7 pages
    for (let i = 0; i < totalPages; i++) {
      pages.push(i);
    }
  } else if (currentPageIndex < maxVisible) {
    // previous maxVisible pages
    for (let i = 0; i < maxVisible; i++) {
      pages.push(i);
    }
    pages.push("...");
    pages.push(totalPages - 1);
  } else if (currentPageIndex >= totalPages - maxVisible) {
    // last maxVisible pages
    pages.push(0);
    pages.push("...");
    for (let i = totalPages - 5; i < totalPages; i++) {
      pages.push(i);
    }
  } else {
    // middle page index
    pages.push(0);
    pages.push("...");
    for (let i = siblingCount; i > 0; i--) {
      pages.push(currentPageIndex - i);
    }
    pages.push(currentPageIndex);
    for (let i = siblingCount; i > 0; i--) {
      pages.push(currentPageIndex + i);
    }
    pages.push("...");
    pages.push(totalPages - 1);
  }

  return pages.map((page, index) =>
    page === "..." ? (
      <span key={`paginationIndex-${index}`} className="px-2">
        ...
      </span>
    ) : (
      <Button
        key={`paginationIndex-${index}`}
        variant={page === currentPageIndex ? "default" : "outline"}
        size="icon"
        className="cursor-pointer size-8 rounded-full"
        onClick={() => {
          table.setPageIndex(Number(page));
        }}
      >
        {(page as number) + 1}
      </Button>
    ),
  );
};

const AnalyticDataTable = <TData, TValue>({
  data,
  columns,
  onTableReady,
  isShowPagination = true,
}: AnalyticTableProps<TData, TValue>) => {
  const table = useReactTable({
    data,
    columns,
    globalFilterFn: (row, _, filterValue) => {
      const landName = row.getValue("landName")?.toString() ?? "";
      const landSerialNumber =
        row.getValue("landSerialNumber")?.toString() ?? "";
      return (
        landName.includes(filterValue) || landSerialNumber.includes(filterValue)
      );
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const totalPages = table.getPageCount();
  const currentPageIndex = table.getState().pagination.pageIndex;

  useEffect(() => {
    onTableReady(table);
  }, [table]);

  return (
    <div>
      <div className="overflow-hidden rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, rowIndex) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {cell.column.id === "index"
                        ? rowIndex +
                          1 +
                          table.getState().pagination.pageIndex *
                            table.getState().pagination.pageSize
                        : flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  無資料
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {isShowPagination && totalPages > 0 && (
        <div className="w-full py-4 flex gap-2">
          <div className="w-1/4">
            <div className="w-full flex justify-start items-center space-x-2">
              <p className="text-sm font-medium">每頁筆數</p>
              <Select
                value={`${table.getState().pagination.pageSize}`}
                onValueChange={(value) => {
                  table.setPageSize(Number(value));
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue
                    placeholder={table.getState().pagination.pageSize}
                  />
                </SelectTrigger>
                <SelectContent>
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-1 justify-center items-center space-x-4">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="icon"
                  variant="outline"
                  className="cursor-pointer size-8 rounded-full"
                  onClick={() => {
                    table.setPageIndex(0);
                  }}
                  disabled={!table.getCanPreviousPage()}
                >
                  <ArrowLeftToLineIcon className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>首頁</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="icon"
                  variant="outline"
                  className="cursor-pointer size-8 rounded-full"
                  onClick={() => {
                    table.previousPage();
                  }}
                  disabled={!table.getCanPreviousPage()}
                >
                  <ArrowLeftIcon className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>上一頁</TooltipContent>
            </Tooltip>

            {getPaginationPages({ table, totalPages, currentPageIndex })}

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="icon"
                  variant="outline"
                  className="cursor-pointer size-8 rounded-full"
                  onClick={() => {
                    table.nextPage();
                  }}
                  disabled={!table.getCanNextPage()}
                >
                  <ArrowRightIcon className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>下一頁</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="icon"
                  variant="outline"
                  className="cursor-pointer size-8 rounded-full"
                  onClick={() => {
                    table.setPageIndex(table.getPageCount() - 1);
                  }}
                  disabled={!table.getCanNextPage()}
                >
                  <ArrowRightToLineIcon className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>尾頁</TooltipContent>
            </Tooltip>
          </div>
          <div className="w-1/4 flex justify-end items-center">
            <div className="w-full flex justify-end items-center space-x-2">
              <p className="text-sm font-medium">目前頁碼</p>
              <Select
                value={`${table.getState().pagination.pageIndex}`}
                onValueChange={(value) => {
                  table.setPageIndex(Number(value));
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue
                    placeholder={table.getState().pagination.pageIndex}
                  ></SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: totalPages }, (_, index) => index).map(
                    (pageIndex) => (
                      <SelectItem key={pageIndex} value={`${pageIndex}`}>
                        {pageIndex + 1}
                      </SelectItem>
                    ),
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticDataTable;
