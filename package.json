{"name": "land-client-web", "version": "0.1.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint ."}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "2.2.6", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "1.2.6", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-query": "^5.84.1", "@tanstack/react-table": "^8.21.3", "@types/leaflet": "^1.9.20", "@types/leaflet.heat": "^0.2.5", "@types/leaflet.markercluster": "^1.5.6", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "d3": "^7.9.0", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "plotly.js-dist-min": "^3.1.0", "leaflet.markercluster": "^1.5.3", "lodash-es": "^4.17.21", "lucide-react": "^0.537.0", "next": "15.4.6", "next-intl": "^4.3.8", "plotly.js": "^3.1.0", "proj4": "^2.19.10", "react": "^19.1.1", "react-dom": "^19.1.1", "react-leaflet": "^5.0.0", "react-plotly.js": "^2.6.0", "shadcn": "^3.2.1", "tailwind-merge": "2.6.0", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^2.1.4", "@eslint/js": "^9.35.0", "@next/eslint-plugin-next": "^15.5.3", "@tailwindcss/postcss": "^4", "@types/d3": "^7.4.3", "@types/lodash-es": "^4.17.12", "@types/plotly.js": "^3.0.6", "@types/plotly.js-dist-min": "^2.3.4", "@types/react-dom": "^19", "@types/react-plotly.js": "^2.6.3", "@types/node": "^20.19.17", "@types/react": "^19.1.13", "@typescript-eslint/eslint-plugin": "^8.43.0", "@typescript-eslint/parser": "^8.43.0", "eslint": "^9.35.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.8", "typescript": "^5"}}