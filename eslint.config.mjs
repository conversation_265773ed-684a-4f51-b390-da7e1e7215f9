// eslint.config.mjs
import js from "@eslint/js";
import nextPlugin from "@next/eslint-plugin-next";
import tsparser from "@typescript-eslint/parser";
import tseslint from "@typescript-eslint/eslint-plugin";
import globals from "globals";

export default [
	js.configs.recommended,
	{ ignores: ['.next/**', 'node_modules/**', 'dist/**'] },

	// 一般程式碼：同時給 browser + node 全域
	{
		files: ['**/*.{ts,tsx,js,jsx}'],
		languageOptions: {
			parser: tsparser,
			parserOptions: { project: './tsconfig.json' }, // 不做型別檢查可拿掉
			globals: { ...globals.browser, ...globals.node },
		},
		plugins: { '@typescript-eslint': tseslint, '@next/next': nextPlugin },
		rules: {
			...nextPlugin.configs.recommended.rules,

			// 用 TS 版本，停用核心版以免重複報錯
			'no-unused-vars': 'off',
			'@typescript-eslint/no-unused-vars': [
				'warn',
				{
					vars: 'all',
					args: 'after-used',
					ignoreRestSiblings: true,
					varsIgnorePattern: '^_',
					argsIgnorePattern: '^_',
					caughtErrors: 'none',
					destructuredArrayIgnorePattern: '^_',
				},
			],

			// console 規則（可調）
			'no-console': ['warn', { allow: ['warn', 'error'] }],
			'no-undef': 'off',
		},
	},

	// 設定檔與腳本（純 Node）
	{
		files: ['**/*.{cjs,mjs,js}'],
		languageOptions: { globals: globals.node },
	},
];
