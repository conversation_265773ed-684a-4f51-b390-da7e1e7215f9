/* eslint-disable @typescript-eslint/no-explicit-any */
// configs
import { API_ENDPOINTS } from "../api";

export const getLandRightsData = async () => {
  try {
    const response = await fetch(API_ENDPOINTS.ANAYLYTICS_LAND_RIGHTS_DATA, {
      method: "GET",
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const { data }: { data: Record<string, any>[] } = await response.json();
    return data;
  } catch (error) {
    throw new Error(`Get Analytics Land Rights Data failed: ${error}`);
  }
};
