"use client";
import React from "react";
import { ColumnDef, type CellContext } from "@tanstack/react-table";

// Typescript
export interface AnalyticHeaderProps {
  id: string;
  cell?: (
    cell: CellContext<AnalyticTableHeaderProps, unknown>,
  ) => string | React.JSX.Element;
  label?: string;
}

export interface AnalyticTableHeaderProps {
  srcId: string;
  column: ColumnDef<AnalyticHeaderProps>;
  header: string;
}

export const getColumns = (
  headers: AnalyticHeaderProps[],
): ColumnDef<AnalyticTableHeaderProps>[] =>
  headers.map(({ id, label = "", cell }) => ({
    accessorKey: id,
    header: label,
    ...(cell && { cell }),
  }));
