/* eslint-disable @typescript-eslint/no-explicit-any */

export const splitString = ({
  sign = /[,，]/,
  target,
}: {
  sign?: RegExp | string;
  target: string;
}) =>
  target
    ? target.split(sign).reduce<string[]>((acc, s) => {
        const trimmed = s.trim();
        trimmed.replace(/[^\p{L}\p{N}\p{Script=Han}]+$/gu, "");
        if (trimmed) {
          acc.push(trimmed);
        }
        return acc;
      }, [])
    : [];
