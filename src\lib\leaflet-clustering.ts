// Leaflet MarkerClusterGroup 工具函數
import { DivIconOptions } from 'leaflet';
export interface ClusterableData {
	lat: number;
	lng: number;
	[key: string]: any;
}

export interface ClusterOptions {
	// 聚合選項
	maxClusterRadius?: number; // 聚合半徑（像素）
	disableClusteringAtZoom?: number; // 停止聚合的縮放層級
	spiderfyOnMaxZoom?: boolean; // 最大縮放時是否展開
	showCoverageOnHover?: boolean; // 懸停時顯示覆蓋範圍
	zoomToBoundsOnClick?: boolean; // 點擊時縮放到邊界
	spiderfyDistanceMultiplier?: number; // 展開距離倍數
	removeOutsideVisibleBounds?: boolean; // 移除視窗外的標記
	animate?: boolean; // 動畫效果
	animateAddingMarkers?: boolean; // 添加標記時的動畫

	// 自定義圖標函數
	iconCreateFunction?: (cluster: any) => any;

	// 聚合樣式
	clusterStyle?: {
		backgroundColor?: string;
		textColor?: string;
		borderColor?: string;
		borderWidth?: number;
		fontSize?: number;
		fontWeight?: string;
		borderRadius?: string;
		minSize?: number;
		maxSize?: number;
	};
}

// 預設聚合選項
export const defaultClusterOptions: ClusterOptions = {
	maxClusterRadius: 80,
	disableClusteringAtZoom: 15,
	spiderfyOnMaxZoom: true,
	showCoverageOnHover: false,
	zoomToBoundsOnClick: true,
	spiderfyDistanceMultiplier: 1,
	removeOutsideVisibleBounds: true,
	animate: true,
	animateAddingMarkers: true,
	clusterStyle: {
		backgroundColor: '#3b82f6',
		textColor: 'white',
		borderColor: 'white',
		borderWidth: 2,
		fontSize: 12,
		fontWeight: 'bold',
		borderRadius: '50%',
		minSize: 30,
		maxSize: 60,
	},
};

// 創建自定義聚合圖標
export function createClusterIcon(cluster: any, options: ClusterOptions = defaultClusterOptions): DivIconOptions {
	const childCount = cluster.getChildCount();
	const style = options.clusterStyle || defaultClusterOptions.clusterStyle!;

	// 根據數量調整大小
	let size = style.minSize || 30;
	if (childCount > 100) {
		size = style.maxSize || 60;
	} else if (childCount > 10) {
		size = Math.min(style.maxSize || 60, (style.minSize || 30) + (childCount / 10) * 10);
	}

	const html = `
    <div style="
      background: ${style.backgroundColor || '#3b82f6'};
      color: ${style.textColor || 'white'};
      border: ${style.borderWidth || 2}px solid ${style.borderColor || 'white'};
      border-radius: ${style.borderRadius || '50%'};
      width: ${size}px;
      height: ${size}px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: ${style.fontSize || 12}px;
      font-weight: ${style.fontWeight || 'bold'};
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      cursor: pointer;
      transition: transform 0.2s ease;
    " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
      ${childCount}
    </div>
  `;

	return {
		html,
		className: 'custom-cluster-icon',
		iconSize: [size, size],
		iconAnchor: [size / 2, size / 2],
	};
}

// 創建帶顏色的聚合圖標（用於不同類型的數據）
export function createColoredClusterIcon(cluster: any, color: string, label?: string) {
	const childCount = cluster.getChildCount();

	// 根據數量調整大小
	let size = 30;
	if (childCount > 100) {
		size = 60;
	} else if (childCount > 10) {
		size = 30 + (childCount / 10) * 10;
		size = Math.min(size, 60);
	}

	const html = `
    <div style="
      background: ${color};
      color: white;
      border: 2px solid white;
      border-radius: 50%;
      width: ${size}px;
      height: ${size}px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: ${size > 40 ? 12 : 10}px;
      font-weight: bold;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      cursor: pointer;
      transition: transform 0.2s ease;
      line-height: 1;
    " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
      <div>${childCount}</div>
      ${label ? `<div style="font-size: ${Math.max(8, size / 6)}px; margin-top: 1px;">${label}</div>` : ''}
    </div>
  `;

	return {
		html,
		className: 'colored-cluster-icon',
		iconSize: [size, size] as [number, number],
		iconAnchor: [size / 2, size / 2] as [number, number],
	};
}

// 創建 MarkerClusterGroup
export async function createMarkerClusterGroup(options: ClusterOptions = {}) {
	try {
		const L = (await import('leaflet')).default;

		// 動態導入 leaflet.markercluster
		await import('leaflet.markercluster');

		// 檢查 MarkerClusterGroup 是否可用
		if (!(L as any).markerClusterGroup) {
			throw new Error('MarkerClusterGroup not available. Make sure leaflet.markercluster is properly loaded.');
		}

		const finalOptions = { ...defaultClusterOptions, ...options };

		// 創建自定義圖標函數
		const iconCreateFunction =
			finalOptions.iconCreateFunction ||
			((cluster: any) => {
				const iconData = createClusterIcon(cluster, finalOptions);
				return L.divIcon(iconData);
			});

		// 創建 MarkerClusterGroup
		const markerClusterGroup = (L as any).markerClusterGroup({
			maxClusterRadius: finalOptions.maxClusterRadius,
			disableClusteringAtZoom: finalOptions.disableClusteringAtZoom,
			spiderfyOnMaxZoom: finalOptions.spiderfyOnMaxZoom,
			showCoverageOnHover: finalOptions.showCoverageOnHover,
			zoomToBoundsOnClick: finalOptions.zoomToBoundsOnClick,
			spiderfyDistanceMultiplier: finalOptions.spiderfyDistanceMultiplier,
			removeOutsideVisibleBounds: finalOptions.removeOutsideVisibleBounds,
			animate: finalOptions.animate,
			animateAddingMarkers: finalOptions.animateAddingMarkers,
			iconCreateFunction: iconCreateFunction,
		});

		console.warn('MarkerClusterGroup created successfully');
		return markerClusterGroup;
	} catch (error) {
		console.error('Error creating MarkerClusterGroup:', error);
		throw error;
	}
}

// 添加標記到聚合組
export async function addMarkersToCluster<T extends ClusterableData>(
	clusterGroup: any,
	data: T[],
	createMarkerFunction: (item: T, L: any) => any
) {
	const L = (await import('leaflet')).default;
console.log('data', data);
	const markers = data.map((item) => createMarkerFunction(item, L));
	clusterGroup.addLayers(markers);

	return markers;
}

// 清除聚合組中的所有標記
export function clearClusterGroup(clusterGroup: any) {
	if (clusterGroup) {
		clusterGroup.clearLayers();
	}
}

// 移除聚合組從地圖
export function removeClusterGroupFromMap(map: any, clusterGroup: any) {
	if (map && clusterGroup) {
		try {
			map.removeLayer(clusterGroup);
		} catch (error) {
			console.warn('Error removing cluster group:', error);
		}
	}
}

// 添加聚合組到地圖
export function addClusterGroupToMap(map: any, clusterGroup: any) {
	if (map && clusterGroup) {
		try {
			clusterGroup.addTo(map);
		} catch (error) {
			console.warn('Error adding cluster group:', error);
		}
	}
}

// 獲取聚合組的統計資訊
export function getClusterStats(clusterGroup: any) {
	if (!clusterGroup) return null;

	const layers = clusterGroup.getLayers();
	const clusters = clusterGroup._featureGroup.getLayers().filter((layer: any) => layer.getChildCount && layer.getChildCount() > 1);

	return {
		totalMarkers: layers.length,
		totalClusters: clusters.length,
		singleMarkers: layers.length - clusters.reduce((sum: number, cluster: any) => sum + cluster.getChildCount(), 0),
		largestCluster: clusters.length > 0 ? Math.max(...clusters.map((c: any) => c.getChildCount())) : 0,
	};
}

// 聚合事件處理器
export interface ClusterEventHandlers {
	onClusterClick?: (cluster: any, event: any) => void;
	onClusterMouseOver?: (cluster: any, event: any) => void;
	onClusterMouseOut?: (cluster: any, event: any) => void;
	onMarkerClick?: (marker: any, event: any) => void;
}

// 添加聚合事件監聽器
export function addClusterEventListeners(clusterGroup: any, handlers: ClusterEventHandlers) {
	if (handlers.onClusterClick) {
		clusterGroup.on('clusterclick', handlers.onClusterClick);
	}

	if (handlers.onClusterMouseOver) {
		clusterGroup.on('clustermouseover', handlers.onClusterMouseOver);
	}

	if (handlers.onClusterMouseOut) {
		clusterGroup.on('clustermouseout', handlers.onClusterMouseOut);
	}

	if (handlers.onMarkerClick) {
		clusterGroup.on('click', handlers.onMarkerClick);
	}
}

// 移除聚合事件監聽器
export function removeClusterEventListeners(clusterGroup: any, handlers: ClusterEventHandlers) {
	if (handlers.onClusterClick) {
		clusterGroup.off('clusterclick', handlers.onClusterClick);
	}

	if (handlers.onClusterMouseOver) {
		clusterGroup.off('clustermouseover', handlers.onClusterMouseOver);
	}

	if (handlers.onClusterMouseOut) {
		clusterGroup.off('clustermouseout', handlers.onClusterMouseOut);
	}

	if (handlers.onMarkerClick) {
		clusterGroup.off('click', handlers.onMarkerClick);
	}
}
