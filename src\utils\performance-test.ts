// 地圖性能測試工具

export interface PerformanceTestResult {
  testName: string;
  dataSize: number;
  renderTime: number;
  memoryUsage?: number;
  fps?: number;
  success: boolean;
  error?: string;
}

export class MapPerformanceTester {
  private results: PerformanceTestResult[] = [];
  private isRunning = false;

  // 測試數據渲染性能
  async testDataRendering(
    testName: string,
    data: any[],
    renderFunction: (data: any[]) => Promise<void>
  ): Promise<PerformanceTestResult> {
    const startTime = performance.now();
    const startMemory = this.getMemoryUsage();

    try {
      await renderFunction(data);
      
      const endTime = performance.now();
      const endMemory = this.getMemoryUsage();
      
      const result: PerformanceTestResult = {
        testName,
        dataSize: data.length,
        renderTime: endTime - startTime,
        memoryUsage: endMemory - startMemory,
        success: true,
      };

      this.results.push(result);
      return result;
    } catch (error) {
      const result: PerformanceTestResult = {
        testName,
        dataSize: data.length,
        renderTime: performance.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };

      this.results.push(result);
      return result;
    }
  }

  // 測試縮放性能
  async testZoomPerformance(
    zoomLevels: number[],
    zoomFunction: (zoom: number) => Promise<void>
  ): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = [];

    for (const zoom of zoomLevels) {
      const result = await this.testDataRendering(
        `Zoom Level ${zoom}`,
        [zoom], // 使用縮放層級作為數據
        async () => await zoomFunction(zoom)
      );
      results.push(result);
    }

    return results;
  }

  // 測試移動性能
  async testPanPerformance(
    positions: Array<{ lat: number; lng: number }>,
    panFunction: (position: { lat: number; lng: number }) => Promise<void>
  ): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = [];

    for (let i = 0; i < positions.length; i++) {
      const position = positions[i];
      const result = await this.testDataRendering(
        `Pan to Position ${i + 1}`,
        [position],
        async () => await panFunction(position)
      );
      results.push(result);
    }

    return results;
  }

  // 批量性能測試
  async runBatchTest(
    testConfigs: Array<{
      name: string;
      dataSize: number;
      generateData: () => any[];
      renderFunction: (data: any[]) => Promise<void>;
    }>
  ): Promise<PerformanceTestResult[]> {
    this.isRunning = true;
    const results: PerformanceTestResult[] = [];

    try {
      for (const config of testConfigs) {
        if (!this.isRunning) break;

        console.log(`Running test: ${config.name}`);
        const data = config.generateData();
        const result = await this.testDataRendering(
          config.name,
          data,
          config.renderFunction
        );
        results.push(result);

        // 等待一段時間讓瀏覽器回收記憶體
        await this.sleep(1000);
      }
    } finally {
      this.isRunning = false;
    }

    return results;
  }

  // 停止測試
  stopTest(): void {
    this.isRunning = false;
  }

  // 獲取測試結果
  getResults(): PerformanceTestResult[] {
    return [...this.results];
  }

  // 清除測試結果
  clearResults(): void {
    this.results = [];
  }

  // 生成測試報告
  generateReport(): string {
    if (this.results.length === 0) {
      return 'No test results available.';
    }

    const successfulTests = this.results.filter(r => r.success);
    const failedTests = this.results.filter(r => !r.success);

    let report = '# 地圖性能測試報告\n\n';
    
    report += `## 測試概要\n`;
    report += `- 總測試數: ${this.results.length}\n`;
    report += `- 成功: ${successfulTests.length}\n`;
    report += `- 失敗: ${failedTests.length}\n`;
    report += `- 成功率: ${((successfulTests.length / this.results.length) * 100).toFixed(1)}%\n\n`;

    if (successfulTests.length > 0) {
      report += `## 性能統計\n`;
      
      const renderTimes = successfulTests.map(r => r.renderTime);
      const avgRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length;
      const maxRenderTime = Math.max(...renderTimes);
      const minRenderTime = Math.min(...renderTimes);

      report += `- 平均渲染時間: ${avgRenderTime.toFixed(2)}ms\n`;
      report += `- 最大渲染時間: ${maxRenderTime.toFixed(2)}ms\n`;
      report += `- 最小渲染時間: ${minRenderTime.toFixed(2)}ms\n\n`;

      const memoryUsages = successfulTests
        .map(r => r.memoryUsage)
        .filter((m): m is number => m !== undefined);
      
      if (memoryUsages.length > 0) {
        const avgMemoryUsage = memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length;
        report += `- 平均記憶體使用: ${(avgMemoryUsage / 1024 / 1024).toFixed(2)}MB\n\n`;
      }
    }

    report += `## 詳細結果\n\n`;
    report += `| 測試名稱 | 數據量 | 渲染時間(ms) | 記憶體使用(MB) | 狀態 |\n`;
    report += `|----------|--------|--------------|----------------|------|\n`;

    for (const result of this.results) {
      const memoryMB = result.memoryUsage 
        ? (result.memoryUsage / 1024 / 1024).toFixed(2)
        : 'N/A';
      
      const status = result.success ? '✅' : '❌';
      
      report += `| ${result.testName} | ${result.dataSize} | ${result.renderTime.toFixed(2)} | ${memoryMB} | ${status} |\n`;
    }

    if (failedTests.length > 0) {
      report += `\n## 失敗的測試\n\n`;
      for (const failed of failedTests) {
        report += `### ${failed.testName}\n`;
        report += `- 錯誤: ${failed.error}\n`;
        report += `- 數據量: ${failed.dataSize}\n\n`;
      }
    }

    return report;
  }

  // 獲取記憶體使用量
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  // 等待指定時間
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 預設測試配置
export const defaultTestConfigs = {
  // 小數據集測試
  smallDataset: {
    name: 'Small Dataset (100 points)',
    dataSize: 100,
    generateData: () => generateMockMapData(100),
    renderFunction: async (data: any[]) => {
      // 模擬渲染
      await new Promise(resolve => setTimeout(resolve, 10));
    },
  },

  // 中等數據集測試
  mediumDataset: {
    name: 'Medium Dataset (1000 points)',
    dataSize: 1000,
    generateData: () => generateMockMapData(1000),
    renderFunction: async (data: any[]) => {
      await new Promise(resolve => setTimeout(resolve, 50));
    },
  },

  // 大數據集測試
  largeDataset: {
    name: 'Large Dataset (5000 points)',
    dataSize: 5000,
    generateData: () => generateMockMapData(5000),
    renderFunction: async (data: any[]) => {
      await new Promise(resolve => setTimeout(resolve, 200));
    },
  },
};

// 生成模擬地圖數據
function generateMockMapData(count: number): any[] {
  const data = [];
  const centerLat = 22.6;
  const centerLng = 120.5;

  for (let i = 0; i < count; i++) {
    data.push({
      id: `point_${i}`,
      lat: centerLat + (Math.random() - 0.5) * 0.5,
      lng: centerLng + (Math.random() - 0.5) * 0.5,
      value: Math.random() * 100,
    });
  }

  return data;
}

// 創建全域測試實例
export const mapPerformanceTester = new MapPerformanceTester();

// 快速測試函數
export async function quickPerformanceTest(): Promise<void> {
  console.log('開始快速性能測試...');
  
  const results = await mapPerformanceTester.runBatchTest([
    defaultTestConfigs.smallDataset,
    defaultTestConfigs.mediumDataset,
    defaultTestConfigs.largeDataset,
  ]);

  console.log('測試完成！');
  console.log(mapPerformanceTester.generateReport());
}

// 在開發環境下自動運行測試
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  // 將測試工具掛載到 window 對象，方便在控制台使用
  (window as any).mapPerformanceTester = mapPerformanceTester;
  (window as any).quickPerformanceTest = quickPerformanceTest;
  
  console.log('地圖性能測試工具已載入！');
  console.log('使用 quickPerformanceTest() 運行快速測試');
  console.log('使用 mapPerformanceTester 進行自定義測試');
}
