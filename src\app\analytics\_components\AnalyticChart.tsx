"use client";

import { useRef, useMemo, useEffect } from "react";
import type Plotly from "plotly.js";

let PlotlyLib: typeof import("plotly.js-dist-min") | null = null;

// shadcn ui
import { Skeleton } from "@/components/ui/skeleton";

// configs
import { CHART_TYPE } from "@/config/chart.config";

// utils
import { cn } from "@/lib/utils";

const transformChartFormat = ({
  type,
  data = [],
  configs = {},
}: {
  type: Plotly.PlotType;
  data?: [string, number][];
  configs?: Record<string, any>;
}) => {
  const {
    title = "",
    margin = { t: 50, r: 50, b: 100, l: 50, pad: 5 },
    initialFrame,
    ...layoutProps
  } = configs;

  switch (type) {
    case "bar": {
      const {
        width = undefined,
        xTitle = "",
        yTitle = "",
        marker = { color: "#104860", line: { width: 2 } },
        barmode = "group" as const,
        autosize = true,
        dragmode = "pan",
        annotations = [],
        textposition = "auto" as const,
        texttemplate = "%{y}件",
      } = configs.bar || {};

      return {
        data: {
          ...(data.length > 0
            ? data?.reduceRight(
                (
                  acc: { x: string[]; y: number[]; text: string[] },
                  [name, quantity],
                ) => {
                  acc.x.push(name.replace(/[,，]+/g, "、"));
                  acc.y.push(quantity);
                  acc.text.push(`${quantity}`);
                  return acc;
                },
                { x: [], y: [], text: [] },
              ) || { x: [], y: [], text: [] }
            : { x: [], y: [], text: [] }),
          type,
          width: width ?? data.length / 15,
          marker,
          textposition,
          texttemplate,
        },

        // layout
        layout: {
          title: { text: title },
          xaxis: { ...(xTitle && { title: { text: xTitle } }) },
          yaxis: {
            ...(yTitle && { title: { text: yTitle, standoff: 20 } }),
          },
          margin,
          barmode,
          autosize,
          dragmode,
          annotations: initialFrame ? [] : annotations,
          ...layoutProps,
        } as Partial<Plotly.Layout>,
      };
    }

    case "pie": {
      const {
        textinfo = data.length > 0 ? "label+percent" : "label",
        automargin = "auto",
        insidetextorientation = "radial",
      } = configs.pie || {};

      return {
        data: {
          ...(data.length > 0
            ? data?.reduceRight(
                (
                  acc: { labels: string[]; values: number[] },
                  [name, quantity],
                ) => {
                  acc.labels.push(name.replace(/[,，]+/g, "、"));
                  acc.values.push(quantity);
                  return acc;
                },
                { labels: [], values: [] },
              ) || { labels: ["無資料"], values: [1] }
            : { labels: ["無資料"], values: [1] }),
          type,
          textinfo,
          automargin,
          ...(configs?.pie?.annotations && {
            annotations: configs.pie.annotations,
          }),
          insidetextorientation,
        },
        layout: {
          title: { text: title },
          margin,
          ...layoutProps,
        },
      };
    }
    default: {
      return {};
    }
  }
};

const AnalyticChart = ({
  result = {},
  chartType = "bar",
  isLoading = false,
  yearPointer,
  chartConfigs = {},
}: {
  result: Record<number, any>;
  chartType?: Plotly.PlotType;
  isLoading?: boolean;
  yearPointer: string;
  chartConfigs?: Record<string, string>;
}) => {
  const allYearsRange = (result && Object.keys(result)) || [];
  const { title, xTitle, yTitle } = chartConfigs;
  const plotRef = useRef<HTMLDivElement>(null);

  const frames = useMemo(
    () =>
      Object.entries(result ?? {})?.map(([year, items]) => {
        const top10 = (
          Object.entries(
            items.reduce(
              (acc: Record<string, number>, item: Record<string, string>) => {
                acc[item.cause] = (acc[item.cause] ?? 0) + 1;
                return acc;
              },
              {},
            ),
          ) as [string, number][]
        )
          .sort((a, b) => b[1] - a[1]) // sort by calculated quantity
          .slice(0, 10); // get top 10

        return {
          name: `${year}`,
          data: [
            {
              ...transformChartFormat({
                type: CHART_TYPE[chartType],
                data: top10,
              }).data,
            } as Plotly.Data,
          ],
        } as Plotly.Frame;
      }) as unknown as Plotly.Frame[],
    [result, chartType],
  );

  const { initialFrame, initialData } = useMemo(() => {
    const initialFrame = frames.find((f) => f.name === yearPointer);
    const emptyData = [
      { ...transformChartFormat({ type: CHART_TYPE[chartType] }).data },
    ];

    return {
      initialFrame,
      initialData: initialFrame?.data || emptyData,
    };
  }, [frames, chartType, yearPointer]);

  const layout: Partial<Plotly.Layout> = useMemo(
    () => ({
      ...transformChartFormat({
        type: CHART_TYPE[chartType],
        configs: {
          title: `${yearPointer}年${title}`,
          margin: { t: 100, r: 50, b: 100, l: 50, pad: 10 },
          yearPointer,
          initialFrame,
          ...(chartType === CHART_TYPE.bar && {
            bar: {
              xTitle,
              yTitle,
              annotations: [
                {
                  text: "尚無資料",
                  xref: "paper",
                  yref: "paper",
                  x: 0.5,
                  y: 0.5,
                  showarrow: false,
                  font: { size: 20, color: "#104860" },
                },
              ],
            },
          }),
          ...(chartType === CHART_TYPE.pie && {
            pie: {
              textinfo: "label+percent",
              automargin: "auto",
              insidetextorientation: "radial",
            },
          }),
        },
      }).layout,
    }),
    [result, chartType, yearPointer, initialFrame],
  );

  const config: Partial<Plotly.Config> = {
    displaylogo: false,
    displayModeBar: true,
    toImageButtonOptions: { format: "png", filename: title },
    modeBarButtonsToRemove: ["select2d", "lasso2d"] as any,
  };

  useEffect(() => {
    (async ({ frames, initialData, config, layout }) => {
      if (!PlotlyLib) {
        PlotlyLib = await import("plotly.js-dist-min");
      }
      if (!plotRef.current) {
        return;
      }

      await PlotlyLib!.newPlot(plotRef.current, initialData, layout, config);
      // add frames
      PlotlyLib.addFrames(plotRef.current, frames);

      if (yearPointer && allYearsRange.includes(yearPointer)) {
        PlotlyLib!.animate(plotRef.current, yearPointer, {
          mode: "immediate",
          frame: { duration: 1000, redraw: true },
          transition: {
            duration: 1000,
            easing: "linear",
            ordering: "traces first",
          },
        });
      }
    })({ frames, initialData, config, layout });
  }, [frames, layout, chartType, yearPointer]);

  return (
    <div className="w-full">
      <Skeleton
        className="h-[30vh] w-full rounded-lg bg-gray-200"
        style={{ display: isLoading ? "inherit" : "none" }}
      />

      <div
        ref={plotRef}
        className={cn(
          "w-full h-full",
          isLoading ? "hidden" : "display-[inherit]",
        )}
      />
    </div>
  );
};

export default AnalyticChart;
