/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import React, { useState, useLayoutEffect } from 'react';
import type Plotly from 'plotly.js';
import { type CellContext } from '@tanstack/react-table';

// Nextjs
import Link from 'next/link';

// lucide icons
import { TableIcon, CircleXIcon, ChartPieIcon, ChartColumnIncreasingIcon, SquareArrowOutUpRightIcon } from 'lucide-react';

// shadcn ui
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Skeleton } from '@/components/ui/skeleton';

// api
import { getLandRightsData } from '@/lib/analytics/landRights-api';

// configs
import { CHART_TYPE } from '@/config/chart.config';

// utils
import { splitString } from '../_utils/handleData';
import { getColumns } from './AnalyticColumns';

// components
import TimeLine from './TimeLine';
import AnalyticChart from './AnalyticChart';
import AnalyticDataTable from './AnalyticDataTable';

// Typescript
import { AnalyticTableHeaderProps } from './AnalyticColumns';

// local confgis
const HEADERS = [
	{
		id: 'index',
		label: 'No.',
		cell: ({ row }: CellContext<AnalyticTableHeaderProps, unknown>) => <div>{row.index + 1}</div>,
	},
	{ id: 'landName', label: '土名（舊地段名）' },
	{ id: 'landSerialNumber', label: '地番（地號）' },
	{
		id: 'cause',
		label: '變更原因',
		isChip: true,
		cell: ({ row, getValue }: CellContext<AnalyticTableHeaderProps, unknown>) => (
			<div className="flex flex-wrap gap-2">
				{splitString({ target: getValue<string>() }).map((item, index) => (
					<Badge variant="outline" className="bg-gray-100 rounded-2xl" key={`${row.original.srcId}-${item}-${index}`}>
						{item}
					</Badge>
				))}
			</div>
		),
	},
	{
		id: 'actions',
		cell: ({ row }: CellContext<AnalyticTableHeaderProps, unknown>) => (
			<Tooltip>
				<TooltipTrigger asChild>
					<Link
						href={`/search?landId=${row.original.srcId}`}
						target="_blank"
						rel="noopener noreferrer"
						className="inline-flex items-center p-2 text-sm font-medium rounded-md transition-all duration-200 text-muted-foreground hover:text-primary hover:bg-primary/5"
					>
						<SquareArrowOutUpRightIcon className="size-4" />
					</Link>
				</TooltipTrigger>
				<TooltipContent>查看內容</TooltipContent>
			</Tooltip>
		),
	},
];

const LandRightAnalytic = ({ analyticItem }: { analyticItem: Record<string, any> }): React.JSX.Element => {
	const [isLoading, setIsLoading] = useState<boolean>(true);
	const [chartType, setChartMode] = useState<Plotly.PlotType | 'table'>('bar');
	const [data, setData] = useState<Record<string, any>>(null!);
	const [yearPointer, setYearPointer] = useState<number>(null!);
	const [defaultYearRange, setDefaultYearRange] = useState<number[]>([]);

	// table
	const [filterText, setFilterText] = useState<string>('');
	const [tableInstance, setTableInstance] = useState<any>(null!);

	const { title } = analyticItem;

	const handleChangeChartMode = (mode: Plotly.PlotType) => {
		setChartMode(mode);
	};

	const resetFilterText = () => {
		setFilterText('');
		tableInstance?.setGlobalFilter('');
	};

	// get data
	useLayoutEffect(() => {
		const fetchData = async () => {
			try {
				await getLandRightsData().then((data) => {
					setData(
						data.reduce((acc, item) => {
							const year = /^\d+$/.test(item.year?.trim() || '') && Number(item.year?.trim());
							if (year) {
								acc[year] = (!acc[year] ? [] : acc[year]).concat([item]);
							}

							return acc;
						}, {})
					);

					setDefaultYearRange(
						data
							.reduce((acc, item) => {
								const year = item.year?.trim();
								if (!acc.includes(Number(year)) && /^\d+$/.test(year)) {
									acc.push(Number(year));
								}
								return acc;
							}, [])
							.sort((a: number, b: number) => a - b)
					);
					setIsLoading(false);

					// console.log(
					//   "卍每一年份中的所有權利變更卍",
					//   data.reduce((acc, item) => {
					//     const year =
					//       /^\d+$/.test(item.year?.trim() || "") &&
					//       Number(item.year?.trim());
					//     if (year) {
					//       acc[year] = (!acc[year] ? [] : acc[year]).concat([item]);
					//     }

					//     return acc;
					//   }, {}),
					// );
				});
			} catch (error) {
				console.log('Error: ', error);
			}
		};

		fetchData();
	}, []);

	useLayoutEffect(() => {
		// reset filter text when year pointer changed
		resetFilterText();
	}, [yearPointer]);

	return (
		<div className="grid grid-rows-[auto_auto_auto] gap-4">
			<div className="p-2 text-2xl font-semibold">{title}</div>
			<div className="">
				<TimeLine
					isLoading={isLoading}
					restartTrigger={(year) => {
						setYearPointer(year);
					}}
					defaultYearRange={defaultYearRange}
				/>
			</div>
			<div className="grid grid-rows-auto gap-4">
				<div className="flex justify-end w-full">
					{isLoading ? (
						<div className="w-full flex justify-between gap-4">
							<Skeleton className="h-[24px] w-[24px] rounded-sm bg-gray-200" />
							<Skeleton className="h-[24px] w-[24px] rounded-sm bg-gray-200" />
						</div>
					) : (
						<div className="w-full flex justify-between items-center">
							{chartType === CHART_TYPE.table && (
								<div className="w-1/2 flex justify-start items-center gap-2">
									{tableInstance && (
										<Input
											placeholder="輸入土名（舊地段名）、地番（地號）進行篩選"
											value={filterText}
											onChange={(event) => {
												const value = event.target.value;
												setFilterText(value);
												tableInstance?.setGlobalFilter(event.target.value);
											}}
											className="w-full rounded-sm"
										/>
									)}

									{filterText && (
										<Tooltip>
											<TooltipTrigger asChild>
												<Button
													size="icon"
													variant="ghost"
													className="cursor-pointer size-8 rounded-full"
													onClick={() => {
														resetFilterText();
													}}
												>
													<CircleXIcon className="size-6" />
												</Button>
											</TooltipTrigger>
											<TooltipContent>清除</TooltipContent>
										</Tooltip>
									)}
								</div>
							)}
							<div className="ml-auto w-fit justify-between space-x-2">
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											size="icon"
											variant="ghost"
											className={`${chartType === 'bar' ? 'bg-blue-50' : ''} cursor-pointer size-8`}
											onClick={() => {
												handleChangeChartMode('bar');
											}}
										>
											<ChartColumnIncreasingIcon className="size-6" />
										</Button>
									</TooltipTrigger>
									<TooltipContent>長條圖</TooltipContent>
								</Tooltip>

								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											size="icon"
											variant="ghost"
											className={`${chartType === 'pie' ? 'bg-blue-50' : ''} cursor-pointer size-8`}
											onClick={() => {
												handleChangeChartMode('pie');
											}}
										>
											<ChartPieIcon className="size-6" />
										</Button>
									</TooltipTrigger>
									<TooltipContent>圓餅圖</TooltipContent>
								</Tooltip>

								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											size="icon"
											variant="ghost"
											className={`${chartType === CHART_TYPE.table ? 'bg-blue-50' : ''} cursor-pointer size-8`}
											onClick={() => {
												handleChangeChartMode(CHART_TYPE.table);
											}}
										>
											<TableIcon className="size-6" />
										</Button>
									</TooltipTrigger>
									<TooltipContent>列表</TooltipContent>
								</Tooltip>
							</div>
						</div>
					)}
				</div>

				{[CHART_TYPE.bar, CHART_TYPE.pie].includes(chartType) && (
					<AnalyticChart
						chartType={chartType}
						result={data}
						yearPointer={`${yearPointer}`}
						isLoading={isLoading}
						chartConfigs={{ title, yTitle: '次數' }}
					/>
				)}
				{chartType === CHART_TYPE.table && (
					<AnalyticDataTable
						data={data[yearPointer] ?? []}
						columns={getColumns(HEADERS)}
						onTableReady={(table) => {
							setTableInstance(table);
						}}
					/>
				)}
			</div>
		</div>
	);
};

export default LandRightAnalytic;
