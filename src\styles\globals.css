@import "tailwindcss";
@import "tw-animate-css";

/* Leaflet MarkerCluster CSS */
@import "leaflet.markercluster/dist/MarkerCluster.css";
@import "leaflet.markercluster/dist/MarkerCluster.Default.css";

/* Leaflet 地圖樣式修正 */
.leaflet-container {
  font-family: inherit;
}

.leaflet-popup-content-wrapper {
  border-radius: 8px;
}

.leaflet-popup-content {
  margin: 12px 16px;
  font-size: 14px;
}

/* 確保地圖控制項在正確的層級 */
.leaflet-control-layers {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.leaflet-control-zoom {
  border-radius: 8px;
  overflow: hidden;
}

.leaflet-control-zoom a {
  border-radius: 0;
}

.leaflet-control-zoom a:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.leaflet-control-zoom a:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@layer theme {
  :root {
    /* Radius */
    --radius: 0.625rem;

    /* Primary Colors - 使用自定義的綠色系統 */
    --color-primary-50: #f3faf8;
    --color-primary-100: #d6F1e9;
    --color-primary-200: #ace3d3;
    --color-primary-300: #7aceb9;
    --color-primary-400: #4fb29d;
    --color-primary-500: #308876;
    --color-primary-600: #28796a;
    --color-primary-700: #246156;
    --color-primary-800: #214e47;
    --color-primary-900: #1f423d;
    --color-primary-950: #0d2624;

    /* Secondary Colors - 使用自定義的藍色系統 */
    --color-secondary-50: #f2f8fd;
    --color-secondary-100: #e3effb;
    --color-secondary-200: #c2e0f5;
    --color-secondary-300: #8bc7ee;
    --color-secondary-400: #4eaae2;
    --color-secondary-500: #2485c2;
    --color-secondary-600: #1871b1;
    --color-secondary-700: #155b8f;
    --color-secondary-800: #154e77;
    --color-secondary-900: #174163;
    --color-secondary-950: #0f2a42;

    /* Tertiary Colors (Orange) */
    --color-tertiary-50: #fff4eb;
    --color-tertiary-100: #ffeed4;
    --color-tertiary-200: #ffdaa8;
    --color-tertiary-300: #ffbe71;
    --color-tertiary-400: #ff9839;
    --color-tertiary-500: #fe7911;
    --color-tertiary-600: #ef5e07;
    --color-tertiary-700: #c64508;
    --color-tertiary-800: #9d370f;
    --color-tertiary-900: #7e2f10;
    --color-tertiary-950: #441506;

    /* Quaternary Colors (Yellow) */
    --color-quaternary-50: #fbfcea;
    --color-quaternary-100: #f4f8c9;
    --color-quaternary-200: #eef296;
    --color-quaternary-300: #eaea5a;
    --color-quaternary-400: #e3dc2c;
    --color-quaternary-500: #d9ca20;
    --color-quaternary-600: #b69018;
    --color-quaternary-700: #917317;
    --color-quaternary-800: #795b1a;
    --color-quaternary-900: #674c1c;
    --color-quaternary-950: #3c290c;

    /* Error Colors */
    --color-error-50: #ffd3f3;
    --color-error-100: #fde3e3;
    --color-error-200: #fbcdcd;
    --color-error-300: #f6a9a9;
    --color-error-400: #f17878;
    --color-error-500: #e74c4c;
    --color-error-600: #d32f2f;
    --color-error-700: #b12424;
    --color-error-800: #932121;
    --color-error-900: #7a2222;

    /* Neutral Colors */
    --color-neutral-white: #ffffff;
    --color-neutral-50: #fafafa;
    --color-neutral-100: #f5f5f5;
    --color-neutral-200: #eeeeee;
    --color-neutral-300: #e0e0e0;
    --color-neutral-400: #c0c0e0;
    --color-neutral-500: #9e9e9e;
    --color-neutral-600: #757575;
    --color-neutral-700: #616161;
    --color-neutral-800: #424242;
    --color-neutral-900: #212121;
    --color-neutral-black: #000000;

    /* shadcn/ui Design System Variables - 映射到自定義顏色系統 */
    --card: var(--color-neutral-white);
    --card-foreground: var(--color-neutral-900);
    --popover: var(--color-neutral-white);
    --popover-foreground: var(--color-neutral-900);
    --primary: var(--color-primary-600);
    --primary-foreground: var(--color-neutral-white);
    --secondary: var(--color-neutral-100);
    --secondary-foreground: var(--color-primary-600);
    --muted: var(--color-neutral-100);
    --muted-foreground: var(--color-neutral-600);
    --accent: var(--color-neutral-100);
    --accent-foreground: var(--color-primary-600);
    --destructive: var(--color-error-500);
    --border: var(--color-neutral-200);
    --input: var(--color-neutral-200);
    --ring: var(--color-primary-500);
    --chart-1: var(--color-primary-500);
    --chart-2: var(--color-secondary-500);
    --chart-3: var(--color-tertiary-500);
    --chart-4: var(--color-quaternary-500);
    --chart-5: var(--color-error-500);
    --sidebar: var(--color-neutral-white);
    --sidebar-foreground: var(--color-neutral-900);
    --sidebar-primary: var(--color-primary-600);
    --sidebar-primary-foreground: var(--color-neutral-white);
    --sidebar-accent: var(--color-neutral-100);
    --sidebar-accent-foreground: var(--color-primary-600);
    --sidebar-border: var(--color-neutral-200);
    --sidebar-ring: var(--color-primary-500);
    --background: var(--color-neutral-white);
    --foreground: var(--color-neutral-900);
  }

  /* 深色主題 */
  @media (prefers-color-scheme: dark) {
    :root {
      /* 深色主題的中性色調整 */
      --color-neutral-50: #212121;
      --color-neutral-100: #424242;
      --color-neutral-900: #f5f5f5;
      --color-neutral-white: #000000;
      --color-neutral-black: #ffffff;

      /* 深色主題的 shadcn/ui 變數 */
      --background: var(--color-neutral-900);
      --foreground: var(--color-neutral-50);
      --card: var(--color-neutral-800);
      --card-foreground: var(--color-neutral-50);
      --popover: var(--color-neutral-800);
      --popover-foreground: var(--color-neutral-50);
      --primary: var(--color-primary-400);
      --primary-foreground: var(--color-neutral-900);
      --secondary: var(--color-neutral-700);
      --secondary-foreground: var(--color-neutral-50);
      --muted: var(--color-neutral-700);
      --muted-foreground: var(--color-neutral-400);
      --accent: var(--color-neutral-700);
      --accent-foreground: var(--color-neutral-50);
      --destructive: var(--color-error-400);
      --border: rgba(255, 255, 255, 0.1);
      --input: rgba(255, 255, 255, 0.15);
      --ring: var(--color-primary-400);
      --chart-1: var(--color-primary-400);
      --chart-2: var(--color-secondary-400);
      --chart-3: var(--color-tertiary-400);
      --chart-4: var(--color-quaternary-400);
      --chart-5: var(--color-error-400);
      --sidebar: var(--color-neutral-800);
      --sidebar-foreground: var(--color-neutral-50);
      --sidebar-primary: var(--color-primary-400);
      --sidebar-primary-foreground: var(--color-neutral-900);
      --sidebar-accent: var(--color-neutral-700);
      --sidebar-accent-foreground: var(--color-neutral-50);
      --sidebar-border: rgba(255, 255, 255, 0.1);
      --sidebar-ring: var(--color-primary-400);
    }
  }

  .dark {
    --background: var(--color-neutral-900);
    --foreground: var(--color-neutral-50);
    --card: var(--color-neutral-800);
    --card-foreground: var(--color-neutral-50);
    --popover: var(--color-neutral-800);
    --popover-foreground: var(--color-neutral-50);
    --primary: var(--color-primary-400);
    --primary-foreground: var(--color-neutral-900);
    --secondary: var(--color-neutral-700);
    --secondary-foreground: var(--color-neutral-50);
    --muted: var(--color-neutral-700);
    --muted-foreground: var(--color-neutral-400);
    --accent: var(--color-neutral-700);
    --accent-foreground: var(--color-neutral-50);
    --destructive: var(--color-error-400);
    --border: rgba(255, 255, 255, 0.1);
    --input: rgba(255, 255, 255, 0.15);
    --ring: var(--color-primary-400);
    --chart-1: var(--color-primary-400);
    --chart-2: var(--color-secondary-400);
    --chart-3: var(--color-tertiary-400);
    --chart-4: var(--color-quaternary-400);
    --chart-5: var(--color-error-400);
    --sidebar: var(--color-neutral-800);
    --sidebar-foreground: var(--color-neutral-50);
    --sidebar-primary: var(--color-primary-400);
    --sidebar-primary-foreground: var(--color-neutral-900);
    --sidebar-accent: var(--color-neutral-700);
    --sidebar-accent-foreground: var(--color-neutral-50);
    --sidebar-border: rgba(255, 255, 255, 0.1);
    --sidebar-ring: var(--color-primary-400);
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}