/* app/globals.css */
@import "tailwindcss";

@layer theme {
  :root {
    /* Primary Colors */
    --color-primary-50: #f3faf8;
    --color-primary-100: #d6F1e9;
    --color-primary-200: #ace3d3;
    --color-primary-300: #7aceb9;
    --color-primary-400: #4fb29d;
    --color-primary-500: #308876;
    --color-primary-600: #28796a;
    --color-primary-700: #246156;
    --color-primary-800: #214e47;
    --color-primary-900: #1f423d;
    --color-primary-950: #0d2624;
    
    /* Secondary Colors */
    --color-secondary-50: #f2f8fd;
    --color-secondary-100: #e3effb;
    --color-secondary-200: #c2e0f5;
    --color-secondary-300: #8bc7ee;
    --color-secondary-400: #4eaae2;
    --color-secondary-500: #2485c2;
    --color-secondary-600: #1871b1;
    --color-secondary-700: #155b8f;
    --color-secondary-800: #154e77;
    --color-secondary-900: #174163;
    --color-secondary-950: #0f2a42;
    
    /* Tertiary Colors (Orange) */
    --color-tertiary-50: #fff4eb;
    --color-tertiary-100: #ffeed4;
    --color-tertiary-200: #ffdaa8;
    --color-tertiary-300: #ffbe71;
    --color-tertiary-400: #ff9839;
    --color-tertiary-500: #fe7911;
    --color-tertiary-600: #ef5e07;
    --color-tertiary-700: #c64508;
    --color-tertiary-800: #9d370f;
    --color-tertiary-900: #7e2f10;
    --color-tertiary-950: #441506;
    
    /* Quaternary Colors (Yellow) */
    --color-quaternary-50: #fbfcea;
    --color-quaternary-100: #f4f8c9;
    --color-quaternary-200: #eef296;
    --color-quaternary-300: #eaea5a;
    --color-quaternary-400: #e3dc2c;
    --color-quaternary-500: #d9ca20;
    --color-quaternary-600: #b69018;
    --color-quaternary-700: #917317;
    --color-quaternary-800: #795b1a;
    --color-quaternary-900: #674c1c;
    --color-quaternary-950: #3c290c;
    
    /* Error Colors */
    --color-error-50: #ffd3f3;
    --color-error-100: #fde3e3;
    --color-error-200: #fbcdcd;
    --color-error-300: #f6a9a9;
    --color-error-400: #f17878;
    --color-error-500: #e74c4c;
    --color-error-600: #d32f2f;
    --color-error-700: #b12424;
    --color-error-800: #932121;
    --color-error-900: #7a2222;
    
    /* Neutral Colors */
    --color-neutral-white: #ffffff;
    --color-neutral-50: #fafafa;
    --color-neutral-100: #f5f5f5;
    --color-neutral-200: #eeeeee;
    --color-neutral-300: #e0e0e0;
    --color-neutral-400: #c0c0e0;
    --color-neutral-500: #9e9e9e;
    --color-neutral-600: #757575;
    --color-neutral-700: #616161;
    --color-neutral-800: #424242;
    --color-neutral-900: #212121;
    --color-neutral-black: #000000;
  }

  /* 可選：為深色主題設定不同的顏色值 */
  @media (prefers-color-scheme: dark) {
    :root {
      /* 在這裡可以覆蓋深色主題的顏色 */
      --color-neutral-50: #212121;
      --color-neutral-100: #424242;
      --color-neutral-900: #f5f5f5;
      --color-neutral-white: #000000;
      --color-neutral-black: #ffffff;
    }
  }
}