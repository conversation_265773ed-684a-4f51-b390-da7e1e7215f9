'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight, Play, Pause, RotateCcw } from 'lucide-react';

interface TimelineSliderProps {
  startYear: number;
  endYear: number;
  currentYear: number;
  minYear: number;
  maxYear: number;
  onChange: (values: { startYear: number; endYear: number; currentYear: number }) => void;
  className?: string;
}

export function TimelineSlider({
  startYear,
  endYear,
  currentYear,
  minYear,
  maxYear,
  onChange,
  className
}: TimelineSliderProps) {
  const [isDragging, setIsDragging] = useState<'start' | 'end' | 'current' | null>(null);
  const sliderRef = useRef<HTMLDivElement>(null);

  // 播放控制
  const [isPlaying, setIsPlaying] = useState(false);
  const [speed, setSpeed] = useState<0.5 | 1 | 2 | 4>(1);
  const [loop, setLoop] = useState(true);

  const rafIdRef = useRef<number | null>(null);
  const lastTsRef = useRef<number | null>(null);
  const carryRef = useRef(0);

  const yearToPercent = useCallback((year: number) => ((year - minYear) / (maxYear - minYear)) * 100, [minYear, maxYear]);
  const percentToYear = useCallback((percent: number) => Math.round(minYear + (percent / 100) * (maxYear - minYear)), [minYear, maxYear]);

  const getPercentFromEvent = useCallback((event: MouseEvent | React.MouseEvent) => {
    if (!sliderRef.current) return 0;
    const rect = sliderRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    return Math.max(0, Math.min(100, (x / rect.width) * 100));
  }, []);

  const handleMouseDown = useCallback((type: 'start' | 'end' | 'current') => (event: React.MouseEvent) => {
    event.preventDefault();
    setIsDragging(type);
    if (isPlaying) setIsPlaying(false);
  }, [isPlaying]);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isDragging) return;
    const percent = getPercentFromEvent(event);
    const year = percentToYear(percent);

    let newStartYear = startYear;
    let newEndYear = endYear;
    let newCurrentYear = currentYear;

    switch (isDragging) {
      case 'start':
        newStartYear = Math.min(year, endYear - 1);
        newCurrentYear = Math.max(newCurrentYear, newStartYear);
        newCurrentYear = Math.min(newCurrentYear, endYear);
        break;
      case 'end':
        newEndYear = Math.max(year, startYear + 1);
        newCurrentYear = Math.max(newCurrentYear, startYear);
        newCurrentYear = Math.min(newCurrentYear, newEndYear);
        break;
      case 'current':
        newCurrentYear = Math.max(startYear, Math.min(endYear, year));
        break;
    }

    onChange({ startYear: newStartYear, endYear: newEndYear, currentYear: newCurrentYear });
  }, [isDragging, startYear, endYear, currentYear, getPercentFromEvent, percentToYear, onChange]);

  const handleMouseUp = useCallback(() => setIsDragging(null), []);

  useEffect(() => {
    if (!isDragging) return;
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const tick = useCallback((ts: number) => {
    if (!isPlaying) return;
    if (lastTsRef.current == null) {
      lastTsRef.current = ts;
      rafIdRef.current = requestAnimationFrame(tick);
      return;
    }
    const dt = (ts - lastTsRef.current) / 1000;
    lastTsRef.current = ts;

    carryRef.current += dt * speed;
    let step = 0;
    if (carryRef.current >= 1) {
      step = Math.floor(carryRef.current);
      carryRef.current -= step;
    }

    if (step > 0) {
      let next = currentYear + step;
      if (next > endYear) {
        if (loop) {
          const overshoot = next - endYear - 1;
          next = startYear + overshoot;
        } else {
          next = endYear;
          setIsPlaying(false);
        }
      }
      onChange({ startYear, endYear, currentYear: next });
    }
    rafIdRef.current = requestAnimationFrame(tick);
  }, [isPlaying, speed, currentYear, startYear, endYear, loop, onChange]);

  useEffect(() => {
    if (isPlaying) {
      lastTsRef.current = null;
      carryRef.current = 0;
      rafIdRef.current = requestAnimationFrame(tick);
    }
    return () => {
      if (rafIdRef.current != null) cancelAnimationFrame(rafIdRef.current);
      rafIdRef.current = null;
    };
  }, [isPlaying, tick]);

  // 鍵盤
  useEffect(() => {
    const onKey = (e: KeyboardEvent) => {
      const t = e.target as HTMLElement | null;
      if (t && /INPUT|TEXTAREA|SELECT/.test(t.tagName)) return;
      if (e.code === 'Space') { e.preventDefault(); setIsPlaying(p => !p); }
      if (e.key === 'ArrowRight') { e.preventDefault(); handleStep(1); }
      if (e.key === 'ArrowLeft') { e.preventDefault(); handleStep(-1); }
    };
    window.addEventListener('keydown', onKey);
    return () => window.removeEventListener('keydown', onKey);
  });

  const startPercent = yearToPercent(startYear);
  const endPercent = yearToPercent(endYear);
  const currentPercent = yearToPercent(currentYear);

  const handleStep = (delta: number) => {
    const y = Math.max(startYear, Math.min(endYear, currentYear + delta));
    onChange({ startYear, endYear, currentYear: y });
  };

  const speeds: Array<{ label: string; value: 0.5 | 1 | 2 | 4 }> = [
    { label: '0.5×', value: 0.5 },
    { label: '1×', value: 1 },
    { label: '2×', value: 2 },
    { label: '4×', value: 4 },
  ];

  return (
		<div className={cn('w-full space-y-6', className)}>
			{/* 上方三欄摘要 */}
			<div className="grid grid-cols-3 gap-4 text-center">
				<div className="p-4 rounded-2xl border bg-muted/30">
					<div className="text-xs text-muted-foreground mb-1">開始年份</div>
					<div className="text-xl font-semibold text-primary-500">{startYear}</div>
				</div>
				<div className="p-4 rounded-2xl border-2 bg-background">
					<div className="text-xs text-muted-foreground mb-1">當前年份</div>
					<div className="text-2xl font-extrabold black">{currentYear}</div>
				</div>
				<div className="p-4 rounded-2xl border bg-muted/30">
					<div className="text-xs text-muted-foreground mb-1">結束年份</div>
					<div className="text-xl font-semibold">{endYear}</div>
				</div>
			</div>

			{/* 時間軸 */}
			<div className="relative">
				<div ref={sliderRef} className="relative h-3 bg-muted rounded-full cursor-pointer">
					<div
						className="absolute h-full bg-foreground rounded-full bg-primary-500"
						style={{ left: `${startPercent}%`, width: `${endPercent - startPercent}%` }}
					/>
					{/* start knob */}
					<div
						className={cn(
							'absolute w-5 h-5 bg-foreground rounded-full cursor-grab -translate-x-1/2 -translate-y-1/2 top-1/2 border-2 border-background shadow',
							isDragging === 'start' && 'cursor-grabbing scale-105'
						)}
						style={{ left: `${startPercent}%` }}
						onMouseDown={handleMouseDown('start')}
						aria-label="開始年份"
					/>
					{/* end knob */}
					<div
						className={cn(
							'absolute w-5 h-5 bg-foreground rounded-full cursor-grab -translate-x-1/2 -translate-y-1/2 top-1/2 border-2 border-background shadow',
							isDragging === 'end' && 'cursor-grabbing scale-105'
						)}
						style={{ left: `${endPercent}%` }}
						onMouseDown={handleMouseDown('end')}
						aria-label="結束年份"
					/>
					{/* current knob */}
					<div
						className={cn(
							'absolute w-7 h-7 bg-background rounded-full cursor-grab -translate-x-1/2 -translate-y-1/2 top-1/2 border-4 border-foreground shadow z-10',
							isDragging === 'current' && 'cursor-grabbing scale-105'
						)}
						style={{ left: `${currentPercent}%` }}
						onMouseDown={handleMouseDown('current')}
						aria-label="當前年份"
						title={`${currentYear}`}
					/>
				</div>

				{/* 刻度與提示 */}
				<div className="mt-5">
					<div className="flex justify-between text-xs text-muted-foreground mb-1">
						<span>{minYear}</span>
						<span>{Math.round((minYear + maxYear) / 2)}</span>
						<span>{maxYear}</span>
					</div>
					<div className="relative h-2">
						{Array.from({ length: 11 }, (_, i) => (
							<div
								key={i}
								className="absolute w-px bg-muted-foreground/30"
								style={{ left: `${i * 10}%`, height: i % 5 === 0 ? '8px' : '4px', top: i % 5 === 0 ? '0' : '2px' }}
							/>
						))}
					</div>
					<div className="mt-2 text-center text-xs text-muted-foreground">
						時間範圍: {endYear - startYear} 年｜位置: {Math.round(((currentYear - startYear) / (endYear - startYear)) * 100)}%
					</div>
				</div>

				{/* ▼ 懸浮膠囊控制列 */}
				<div className="pointer-events-auto">
					{/* 外層寬度：小螢幕佔滿，避免溢出；中大螢幕收斂 */}
					<div className="mx-auto mt-4 w-full max-w-[560px] px-2">
						<div
							className={cn(
								'flex items-center justify-center gap-2',
								'rounded-full border bg-card/80 backdrop-blur px-2 py-2 shadow-sm',
								'flex-wrap' // 允許換行
							)}
						>
							{/* step -1 */}
							<button
								type="button"
								onClick={() => handleStep(-1)}
								className="inline-flex h-9 w-9 items-center justify-center rounded-full border hover:bg-muted"
								aria-label="上一年"
								title="上一年 (←)"
							>
								<ChevronLeft className="h-4 w-4" />
							</button>

							{/* play / pause */}
							<button
								type="button"
								onClick={() => setIsPlaying((p) => !p)}
								className={cn(
									'inline-flex h-10 w-10 items-center justify-center rounded-full border',
									isPlaying ? 'bg-foreground text-background border-foreground' : 'hover:bg-muted'
								)}
								aria-pressed={isPlaying}
								aria-label={isPlaying ? '暫停' : '播放'}
								title="播放/暫停 (Space)"
							>
								{isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
							</button>

							{/* step +1 */}
							<button
								type="button"
								onClick={() => handleStep(1)}
								className="inline-flex h-9 w-9 items-center justify-center rounded-full border hover:bg-muted"
								aria-label="下一年"
								title="下一年 (→)"
							>
								<ChevronRight className="h-4 w-4" />
							</button>

							{/* 分隔（小螢幕隱藏，省空間） */}
							<div className="mx-1 hidden h-5 w-px bg-muted sm:block" />

							{/* 倍速：sm 以上用分段按鈕；小螢幕改下拉 */}
							<div className="hidden items-center gap-1 sm:flex">
								{([0.5, 1, 2, 4] as const).map((v) => (
									<button
										key={v}
										type="button"
										onClick={() => setSpeed(v)}
										className={cn(
											'px-2 h-8 text-xs rounded-full border',
											speed === v ? 'bg-foreground text-background border-foreground' : 'hover:bg-muted'
										)}
										aria-pressed={speed === v}
										aria-label={`倍速 ${v}x`}
									>
										{v}×
									</button>
								))}
							</div>

							{/* 小螢幕下拉選單 */}
							<div className="flex items-center gap-2 sm:hidden">
								<span className="text-xs text-muted-foreground">倍速</span>
								<select
									value={speed}
									onChange={(e) => setSpeed(Number(e.target.value) as 0.5 | 1 | 2 | 4)}
									className="h-9 rounded-full border bg-background px-3 text-xs"
									aria-label="倍速"
								>
									<option value={0.5}>0.5×</option>
									<option value={1}>1×</option>
									<option value={2}>2×</option>
									<option value={4}>4×</option>
								</select>
							</div>

							{/* 循環：圖示在 xs，文字到 sm 才顯示 */}
							<label className="inline-flex items-center gap-2 text-xs select-none cursor-pointer">
								<input
									type="checkbox"
									className="h-4 w-4 accent-current"
									checked={loop}
									onChange={(e) => setLoop(e.target.checked)}
									aria-label="循環播放"
								/>
								<span className="hidden sm:inline">循環</span>
								<RotateCcw className="h-4 w-4 sm:hidden" />
							</label>
						</div>
					</div>
				</div>
				{/* ▲控制列 */}
			</div>
		</div>
  );
}
