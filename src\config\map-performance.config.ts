// 地圖性能優化配置

export interface LayerPerformanceConfig {
  // 最大渲染點數配置
  maxPoints: {
    [zoomLevel: string]: number;
  };
  
  // 聚合配置
  clustering: {
    enabled: boolean;
    maxZoomForClustering: number;
    minPointsForCluster: number;
    clusterRadius: number; // 公里
  };
  
  // 視窗過濾配置
  viewport: {
    enabled: boolean;
    bufferRatio: number; // 緩衝區比例
    updateDelay: number; // 防抖延遲（毫秒）
  };
  
  // 採樣配置
  sampling: {
    enabled: boolean;
    strategy: 'uniform' | 'random' | 'grid';
  };
}

// 預設性能配置
export const defaultPerformanceConfig: LayerPerformanceConfig = {
  maxPoints: {
    'low': 50,      // 縮放層級 <= 11
    'medium': 500,  // 縮放層級 12-14
    'high': 1000,   // 縮放層級 >= 15
  },
  clustering: {
    enabled: true,
    maxZoomForClustering: 11,
    minPointsForCluster: 2,
    clusterRadius: 1, // 1公里
  },
  viewport: {
    enabled: true,
    bufferRatio: 0.2, // 20% 緩衝區
    updateDelay: 300, // 300毫秒防抖
  },
  sampling: {
    enabled: true,
    strategy: 'uniform',
  },
};

// 不同圖層的特定配置
export const layerConfigs = {
  landType: {
    ...defaultPerformanceConfig,
    maxPoints: {
      'low': 30,      // 地目標記較複雜，減少數量
      'medium': 300,
      'high': 800,
    },
    clustering: {
      ...defaultPerformanceConfig.clustering,
      maxZoomForClustering: 11,
    },
  },
  
  surname: {
    ...defaultPerformanceConfig,
    maxPoints: {
      'low': 50,
      'medium': 500,
      'high': 1000,
    },
    clustering: {
      ...defaultPerformanceConfig.clustering,
      maxZoomForClustering: 11,
    },
  },
  
  landLocation: {
    ...defaultPerformanceConfig,
    maxPoints: {
      'low': 100,     // 土地坐落標記較簡單，可以多一些
      'medium': 500,
      'high': 1000,
    },
    clustering: {
      ...defaultPerformanceConfig.clustering,
      maxZoomForClustering: 12, // 稍微高一點的聚合層級
    },
  },
  
  heatmap: {
    ...defaultPerformanceConfig,
    maxPoints: {
      'low': 200,     // 熱力圖可以處理更多點
      'medium': 1000,
      'high': 5000,
    },
    clustering: {
      enabled: false, // 熱力圖不需要聚合
      maxZoomForClustering: 0,
      minPointsForCluster: 0,
      clusterRadius: 0,
    },
  },
};

// 根據縮放層級獲取最大點數
export function getMaxPointsForZoom(zoom: number, layerType: keyof typeof layerConfigs = 'landType'): number {
  const config = layerConfigs[layerType];
  
  if (zoom <= 11) {
    return config.maxPoints.low;
  } else if (zoom <= 14) {
    return config.maxPoints.medium;
  } else {
    return config.maxPoints.high;
  }
}

// 檢查是否應該使用聚合
export function shouldUseClustering(zoom: number, layerType: keyof typeof layerConfigs = 'landType'): boolean {
  const config = layerConfigs[layerType];
  return config.clustering.enabled && zoom <= config.clustering.maxZoomForClustering;
}

// 獲取視窗過濾配置
export function getViewportConfig(layerType: keyof typeof layerConfigs = 'landType') {
  return layerConfigs[layerType].viewport;
}

// 性能監控配置
export const performanceMonitoring = {
  enabled: process.env.NODE_ENV === 'development',
  logLevel: 'warn' as 'log' | 'warn' | 'error',
  showRenderStats: true,
  showMemoryUsage: false,
};

// 動態性能調整
export class PerformanceManager {
  private static instance: PerformanceManager;
  private performanceMetrics: {
    renderTime: number[];
    memoryUsage: number[];
    frameRate: number[];
  } = {
    renderTime: [],
    memoryUsage: [],
    frameRate: [],
  };

  static getInstance(): PerformanceManager {
    if (!PerformanceManager.instance) {
      PerformanceManager.instance = new PerformanceManager();
    }
    return PerformanceManager.instance;
  }

  // 記錄渲染時間
  recordRenderTime(time: number) {
    this.performanceMetrics.renderTime.push(time);
    // 只保留最近 10 次記錄
    if (this.performanceMetrics.renderTime.length > 10) {
      this.performanceMetrics.renderTime.shift();
    }
  }

  // 獲取平均渲染時間
  getAverageRenderTime(): number {
    const times = this.performanceMetrics.renderTime;
    if (times.length === 0) return 0;
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  // 根據性能動態調整配置
  getOptimizedConfig(layerType: keyof typeof layerConfigs, zoom: number): LayerPerformanceConfig {
    const baseConfig = layerConfigs[layerType];
    const avgRenderTime = this.getAverageRenderTime();
    
    // 如果渲染時間過長（超過 100ms），減少最大點數
    if (avgRenderTime > 100) {
      const reductionFactor = Math.min(0.5, 100 / avgRenderTime);
      return {
        ...baseConfig,
        maxPoints: {
          low: Math.floor(baseConfig.maxPoints.low * reductionFactor),
          medium: Math.floor(baseConfig.maxPoints.medium * reductionFactor),
          high: Math.floor(baseConfig.maxPoints.high * reductionFactor),
        },
      };
    }
    
    return baseConfig;
  }

  // 清除性能記錄
  clearMetrics() {
    this.performanceMetrics = {
      renderTime: [],
      memoryUsage: [],
      frameRate: [],
    };
  }
}

// 性能測量工具
export function measurePerformance<T>(
  name: string,
  fn: () => T,
  logResult: boolean = performanceMonitoring.enabled
): T {
  const startTime = performance.now();
  const result = fn();
  const endTime = performance.now();
  const duration = endTime - startTime;

  // 記錄到性能管理器
  PerformanceManager.getInstance().recordRenderTime(duration);

  if (logResult) {
    const level = performanceMonitoring.logLevel;
    const message = `${name}: ${duration.toFixed(2)}ms`;
    
    if (level === 'error' && duration > 100) {
      console.error(message);
    } else if (level === 'warn' && duration > 50) {
      console.warn(message);
    } else if (level === 'log') {
      console.log(message);
    }
  }

  return result;
}

// 記憶體使用監控
export function monitorMemoryUsage() {
  if (!performanceMonitoring.enabled || !performanceMonitoring.showMemoryUsage) {
    return;
  }

  if ('memory' in performance) {
    const memory = (performance as any).memory;
    console.log('Memory Usage:', {
      used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
    });
  }
}
