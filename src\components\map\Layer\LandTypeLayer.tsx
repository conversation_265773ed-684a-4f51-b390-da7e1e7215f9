'use client';

import { useEffect, useState, useMemo } from 'react';
import { useMap } from 'react-leaflet';
import { LandTypeData, landTypeConfig } from '@/lib/feature-data';
import { filterDataByBounds, sampleDataByZoom, debounce, type MapBounds } from '@/lib/map-performance';
import { getMaxPointsForZoom, getViewportConfig, measurePerformance } from '@/config/map-performance.config';
import {
	createMarkerClusterGroup,
	addMarkersToCluster,
	removeClusterGroupFromMap,
	addClusterGroupToMap,
	createColoredClusterIcon,
	addClusterEventListeners,
	type ClusterEventHandlers,
} from '@/lib/leaflet-clustering';

interface LandTypeLayerProps {
	data: LandTypeData[];
	visible?: boolean;
}

// 創建地目形狀 SVG
function createLandTypeShape(shape: string, size: number, color: string): string {
	const center = size / 2;
	const radius = size / 3;

	switch (shape) {
		case 'circle':
			return `<circle cx="${center}" cy="${center}" r="${radius}" fill="${color}" stroke="white" stroke-width="1"/>`;
		case 'square':
			return `<rect x="${center - radius}" y="${center - radius}" width="${radius * 2}" height="${
				radius * 2
			}" fill="${color}" stroke="white" stroke-width="1"/>`;
		case 'triangle':
			return `<polygon points="${center},${center - radius} ${center - radius},${center + radius} ${center + radius},${
				center + radius
			}" fill="${color}" stroke="white" stroke-width="1"/>`;
		case 'diamond':
			return `<polygon points="${center},${center - radius} ${center + radius},${center} ${center},${center + radius} ${
				center - radius
			},${center}" fill="${color}" stroke="white" stroke-width="1"/>`;
		default:
			return `<circle cx="${center}" cy="${center}" r="${radius}" fill="${color}" stroke="white" stroke-width="1"/>`;
	}
}

export function LandTypeLayer({ data, visible = true }: LandTypeLayerProps) {
	const map = useMap();
	const [currentZoom, setCurrentZoom] = useState(10);
	const [mapBounds, setMapBounds] = useState<MapBounds | null>(null);

	// 使用 useMemo 優化數據處理
	const optimizedData = useMemo(() => {
		return measurePerformance('LandTypeLayer Data Processing', () => {
			if (!data.length || !mapBounds) return data;

			const viewportConfig = getViewportConfig('landType');

			// 1. 先根據視窗範圍過濾
			const boundsFiltered = viewportConfig.enabled ? filterDataByBounds(data, mapBounds, viewportConfig.bufferRatio) : data;

			// 2. 根據縮放層級進行採樣
			const maxPoints = getMaxPointsForZoom(currentZoom, 'landType');
			const sampled = sampleDataByZoom(boundsFiltered, currentZoom, maxPoints);

			return sampled;
		});
	}, [data, mapBounds, currentZoom]);

	// 防抖的邊界更新函數
	const debouncedUpdateBounds = useMemo(() => {
		const viewportConfig = getViewportConfig('landType');
		return debounce((bounds: MapBounds) => {
			setMapBounds(bounds);
		}, viewportConfig.updateDelay);
	}, []);

	// 監聽縮放和移動變化
	useEffect(() => {
		if (!map) return;

		// 設置初始值
		setCurrentZoom(map.getZoom());
		const bounds = map.getBounds();
		setMapBounds({
			north: bounds.getNorth(),
			south: bounds.getSouth(),
			east: bounds.getEast(),
			west: bounds.getWest(),
		});

		// 監聽縮放變化
		const handleZoomEnd = () => {
			const newZoom = map.getZoom();
			setCurrentZoom(newZoom);

			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		// 監聽移動變化
		const handleMoveEnd = () => {
			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		map.on('zoomend', handleZoomEnd);
		map.on('moveend', handleMoveEnd);

		return () => {
			map.off('zoomend', handleZoomEnd);
			map.off('moveend', handleMoveEnd);
		};
	}, [map, debouncedUpdateBounds]);

	useEffect(() => {
		if (!map) return;

		let clusterGroup: any = null;
		let isMounted = true;

		const setupClusterLayer = async () => {
			try {
				// 檢查組件是否仍然掛載
				if (!isMounted) return;

				// 只有當可見且有數據時才創建標記
				if (visible && optimizedData.length > 0) {
					// 性能監控：只在開發環境下輸出
					if (process.env.NODE_ENV === 'development') {
						console.warn(`LandTypeLayer: 渲染 ${optimizedData.length}/${data.length} 個標記 (縮放: ${currentZoom})`);
					}

					// 動態導入 Leaflet
					const L = (await import('leaflet')).default;

					// 創建 MarkerClusterGroup
					clusterGroup = await createMarkerClusterGroup({
						maxClusterRadius: 80,
						disableClusteringAtZoom: 15,
						spiderfyOnMaxZoom: true,
						zoomToBoundsOnClick: true,
						showCoverageOnHover: false,
						iconCreateFunction: (cluster: any) => {
							const iconData = createColoredClusterIcon(cluster, '#3b82f6', '地目');
							return L.divIcon(iconData);
						},
					});

					// 添加聚合事件監聽器
					const eventHandlers: ClusterEventHandlers = {
						onClusterClick: (event: any) => {
							// 點擊聚合時放大到適當層級
							const cluster = event.layer || event.target;
							if (cluster && cluster.getAllChildMarkers) {
								const childMarkers = cluster.getAllChildMarkers();
								if (childMarkers.length > 0) {
									const group = L.featureGroup(childMarkers);
									map.fitBounds(group.getBounds(), {
										padding: [20, 20],
										maxZoom: 15,
									});
								}
							}
						},
					};

					addClusterEventListeners(clusterGroup, eventHandlers);

					// 創建標記函數
					const createMarkerFunction = (item: LandTypeData, L: any) => {
						const config = landTypeConfig[item.landType as keyof typeof landTypeConfig] || {
							color: '#6b7280',
							shape: 'circle' as const,
							name: item.landType,
						};

						const size = 20;
						const shapeHtml = `
              <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}">
                ${createLandTypeShape(config.shape, size, config.color)}
              </svg>
            `;

						const landTypeIcon = L.divIcon({
							html: `
                <div style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
                ">
                  ${shapeHtml}
                  <div style="
                    background: rgba(255, 255, 255, 0.9);
                    color: #374151;
                    padding: 1px 4px;
                    border-radius: 3px;
                    font-size: 10px;
                    font-weight: bold;
                    margin-top: 2px;
                    border: 1px solid rgba(0,0,0,0.1);
                    white-space: nowrap;
                  ">
                    ${config.name}
                  </div>
                </div>
              `,
							className: 'land-type-marker',
							iconSize: [size + 8, size + 16] as [number, number],
							iconAnchor: [(size + 8) / 2, size + 16] as [number, number],
						});

						const marker = L.marker([item.lat, item.lng], { icon: landTypeIcon });

						marker.bindPopup(`
              <div style="font-size: 14px;">
                <strong>地目分布</strong><br/>
                <strong>地目:</strong> ${item.landType}<br/>
                <strong>地號:</strong> ${item.landNumber}<br/>
                <strong>面積:</strong> ${item.area.toLocaleString()} (甲)<br/>
                <strong>地租:</strong> ${item.rent.toLocaleString()} (圓)<br/>
                <strong>年份:</strong> ${item.year}
              </div>
            `);

						return marker;
					};

					// 添加標記到聚合組
					await addMarkersToCluster(clusterGroup, optimizedData, createMarkerFunction);

					// 添加到地圖
					if (isMounted) {
						addClusterGroupToMap(map, clusterGroup);
					}
				}
			} catch (error) {
				console.error('Error setting up land type cluster layer:', error);
			}
		};

		setupClusterLayer();

		return () => {
			isMounted = false;
			if (clusterGroup) {
				removeClusterGroupFromMap(map, clusterGroup);
			}
		};
	}, [map, optimizedData, visible, currentZoom]);

	return null;
}
