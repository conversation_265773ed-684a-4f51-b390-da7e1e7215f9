// 'use client'

// import React from 'react';
// import { Search, MapPin, X, ChevronDown } from "lucide-react";
// import { useState, useRef, useEffect } from "react";
// import DetailPanel from "./DetailModal";
// import { useRouter, useSearchParams } from "next/navigation";

// export default function SearchPage() {

//   const [searchQuery, setSearchQuery] = useState('');
//   const [selectedDetail, setSelectedDetail] = useState<any>(null);
//   const [isOpen, setIsOpen] = useState(false);
//   const [currentPage, setCurrentPage] = useState(1);
//   const [results, setResults] = useState<any[]>([]);
//   const [totalCount, setTotalCount] = useState(0);
//   const [loading, setLoading] = useState(false);
//   const resultsPerPage = 10;
//   const router = useRouter();

//   const dropdownRef = useRef<HTMLDivElement>(null);
//   const searchParams = useSearchParams();
//   const landId = searchParams.get("landId"); // ← 從網址讀取 id

//   const options = [
//     { value: "landName", label: "地段" },
//     { value: "boxNumber", label: "箱號" },
//     { value: "collectionPlace", label: "原典藏地" },
//     { value: "landSerialNumber", label: "地號" },
//     { value: "number", label: "編號" },
//     { value: "pawnRight", label: "典權胎權" },
//     { value: "plowingRight", label: "贌耕權" },
//     { value: "source", label: "現存資料" }
//   ];

//   const [selectedOptions, setSelectedOptions] = useState<string[]>(options.map(o => o.value));

//   useEffect(() => {
//     // 1️⃣ 點擊外部關閉下拉選單
//     function handleClickOutside(event: MouseEvent) {
//       if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
//         setIsOpen(false);
//       }
//     }
//     document.addEventListener('mousedown', handleClickOutside);

//     // 2️⃣ 每次 landId 或 results 改變時，更新 DetailPanel 的資料
//     if (landId) {
//       const detail = results.find(r => r.id === landId);
//       if (detail) setSelectedDetail(detail);
//     } else {
//       setSelectedDetail(null);
//     }

//     return () => {
//       document.removeEventListener('mousedown', handleClickOutside);
//     };
//   }, [landId, results]);

//   // 顯示詳情（網址帶上 id）
//   const handleShowDetail = (id: string) => {
//     router.push(`/search?landId=${id}`);
//   };

//   const fetchPage = async (page: number) => {
//     if (!searchQuery) return;
//     setLoading(true);

//     try {
//       const offset = (page - 1) * resultsPerPage;

//       const params1 = new URLSearchParams({
//         ds: 'south',
//         keyword: searchQuery,
//         limit: resultsPerPage.toString(),
//         offset: offset.toString(),
//       });

//       // 如果有指定搜尋欄位，加入 ids 參數
//       if (selectedOptions.length > 0) {
//         params1.append('ids', selectedOptions.join(','));
//       } else {
//         params1.append('ids', ''); // 沒有選擇欄位時，ids 為空（搜尋所有欄位）
//       }

//       // 使用 1.1 版本的 API
//       const res1 = await fetch(`https://api2.daoyidh.com/land2/zh-hans/client/land/list/search/1.1?${params1.toString()}`);
//       const data1 = await res1.json();
//       setTotalCount(data1.total || 0);

//       // 後續處理保持不變...
//       const ids = data1.data.map((item: any) => item.srcId).join(',');
//       if (!ids) {
//         setResults([]);
//         setLoading(false);
//         return;
//       }

//       const params2 = new URLSearchParams({
//         ds: 'south',
//         ids: '',
//         limit: '-1',
//         offset: '0',
//       });

//       const res2 = await fetch(
//         `https://api2.daoyidh.com/land2/zh-hans/POST/client/land/detail/1.0?${params2.toString()}`,
//         {
//           method: 'POST',
//           headers: { 'Content-Type': 'application/json' },
//           body: JSON.stringify({ entry: { ids } }),
//         }
//       );

//       const data2 = await res2.json();
//       const rawDetails = data2.data || [];

//       const formattedResults = Object.values(
//         rawDetails.reduce((acc: Record<string, any>, item: any) => {
//           if (!acc[item.landId]) acc[item.landId] = { id: item.landId };
//           acc[item.landId][item.key] = item.value;
//           return acc;
//         }, {})
//       );

//       setResults(formattedResults);
//     } catch (error) {
//       console.error(error);
//       setResults([]);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
//     if (e.key === "Enter") {
//       handleSearch();
//     }
//   };

//   const totalPages = Math.ceil(totalCount / resultsPerPage);

//   const goNext = () => {
//     if (currentPage < totalPages) {
//       const nextPage = currentPage + 1;
//       setCurrentPage(nextPage);
//       fetchPage(nextPage);
//       window.scrollTo({ top: 0, behavior: 'smooth' });
//     }
//   };

//   const goPrev = () => {
//     if (currentPage > 1) {
//       const prevPage = currentPage - 1;
//       setCurrentPage(prevPage);
//       fetchPage(prevPage);
//       window.scrollTo({ top: 0, behavior: 'smooth' });
//     }
//   };

//   const handleSearch = () => {
//     setCurrentPage(1);
//     fetchPage(1);
//   };

//   const toggleOption = (value: string) => {
//     setSelectedOptions(prev => prev.includes(value) ? prev.filter(v => v !== value) : [...prev, value]);
//   };

//   const toggleAll = () => {
//     if (selectedOptions.length === options.length) setSelectedOptions([]);
//     else setSelectedOptions(options.map(o => o.value));
//   };

//   const removeOption = (value: string) => setSelectedOptions(prev => prev.filter(v => v !== value));

//   return (
//     <div className="min-h-screen bg-gray-50">
//       <div className="container mx-auto px-4 py-8">
//         <div className="max-w-6xl mx-auto">
//           {selectedDetail ? (
//             <DetailPanel
//               data={selectedDetail}
//               onClose={() => {
//                 router.push("/search", { scroll: false }); // 回到 /search
//                 setSelectedDetail(null);                   // 清除 state
//               }}
//             />
//           ) : (
//             <>
//               {/* Header */}
//               <div className="mb-8">
//                 <h1 className="text-3xl font-bold text-gray-900 mb-4 flex items-center">
//                   <Search className="w-8 h-8 mr-3 text-blue-600" />
//                   土地資訊搜尋
//                 </h1>
//                 <p className="text-lg text-gray-600">
//                   快速搜尋土地資訊，支援多條件篩選與進階查詢功能
//                 </p>
//               </div>

//               <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 mb-8">
//               <div className="flex flex-col md:flex-row gap-4">

//                 {/* 搜尋框 */}
//                 <div className="flex-1">
//                   <div className="relative">
//                     <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
//                     <input
//                       type="text"
//                       onKeyDown={handleKeyDown}   // 🔑 在這裡加
//                       placeholder="請輸入地址、地號或關鍵字..."
//                       className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg
//                                 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
//                       value={searchQuery}
//                       onChange={(e) => setSearchQuery(e.target.value)}
//                     />
//                   </div>
//                 </div>

//               <div className="flex flex-col gap-4">
//                 {/* 下拉選單 */}
//                 <div className="relative w-64" ref={dropdownRef}>
//                   <button
//                     onClick={() => setIsOpen(!isOpen)}
//                     className="w-full flex justify-between items-center px-3 py-3 border border-gray-300 rounded-md bg-white"
//                   >
//                     <span>
//                       {selectedOptions.length > 0
//                         ? `已選 ${selectedOptions.length} 項`
//                         : "請選擇搜尋類別"}
//                     </span>
//                     <ChevronDown className="w-4 h-4" />
//                   </button>

//                   {isOpen && (
//                     <div className="absolute mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg z-10 max-h-60 overflow-auto">
//                       <div className="p-2">
//                         {/* 全選 */}
//                         <label className="flex items-center space-x-2 px-2 py-1 hover:bg-gray-100 rounded cursor-pointer">
//                           <input
//                             type="checkbox"
//                             checked={selectedOptions.length === options.length}
//                             onChange={toggleAll}
//                           />
//                           <span className="text-sm">全選</span>
//                         </label>

//                         <hr className="my-1" />

//                         {/* 個別選項 */}
//                         {options.map((opt) => (
//                           <label
//                             key={opt.value}
//                             className="flex items-center space-x-2 px-2 py-1 hover:bg-gray-100 rounded cursor-pointer"
//                           >
//                             <input
//                               type="checkbox"
//                               checked={selectedOptions.includes(opt.value)}
//                               onChange={() => toggleOption(opt.value)}
//                             />
//                             <span className="text-sm">{opt.label}</span>
//                           </label>
//                         ))}
//                       </div>
//                     </div>

//                   )}
//                 </div>
//                  </div>

//                 {/* 搜尋按鈕 */}
//                 <button
//                   onClick={handleSearch}
//                   className="px-6 py-3 bg-blue-600 text-white rounded-lg
//                             hover:bg-blue-700 transition-colors flex items-center"
//                 >
//                   <Search className="w-5 h-5 mr-2" />
//                   搜尋
//                 </button>
//               </div>
//             </div>

//               {/* 已選條件 */}
//               <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 mb-8">
//                 <label className="block text-sm font-medium text-gray-700 mb-2">已選條件</label>
//                 <div className="flex flex-wrap gap-2">
//                   {selectedOptions.length === 0 && <span className="text-gray-400 text-sm">尚未選擇</span>}
//                   {selectedOptions.map(value => {
//                     const label = options.find(o => o.value === value)?.label || value;
//                     return (
//                       <div key={value} className="flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
//                         {label}
//                         <button onClick={() => removeOption(value)} className="ml-2 text-blue-500 hover:text-blue-700">
//                           <X className="w-4 h-4" />
//                         </button>
//                       </div>
//                     )
//                   })}
//                 </div>
//               </div>

//               {/* 搜尋結果 */}
//               <div className="bg-white rounded-lg shadow-lg border border-gray-200">
//                 <div className="p-6 border-b border-gray-200">
//                   <h2 className="text-xl font-semibold text-gray-900">
//                     搜尋結果 ({totalCount} 筆)
//                   </h2>
//                 </div>

//                 {loading ? (
//                   <div className="p-6 text-center text-gray-500">搜尋中...</div>
//                 ) : results.length === 0 ? (
//                   <div className="p-6 text-center text-gray-500">目前沒有搜尋結果</div>
//                 ) : (
//                   <div className="divide-y divide-gray-200">
//                     {results.map(result => (
//                       <div key={result.id} className="p-6 hover:bg-gray-50 transition-colors">
//                         <div className="flex flex-col md:flex-row md:items-start justify-between gap-4">
//                           <div className="flex-1 md:flex-none md:w-1/3">
//                             <div className="flex items-center mb-2">
//                               <MapPin className="w-4 h-4 text-gray-400 mr-2" />
//                               <h3 className="text-lg font-medium text-gray-900">{result.landName || "無資料"}</h3>
//                             </div>
//                           </div>

//                           <div className="flex-1 flex flex-col gap-2 text-sm text-gray-600 md:ml-50">
//                             <div><span className="font-medium">箱號：</span>{result.boxNumber || '-'}</div>
//                             <div><span className="font-medium">原典藏地：</span>{result.collectionPlace || '-'}</div>
//                             <div><span className="font-medium">地號：</span>{result.landSerialNumber || '-'}</div>
//                             <div><span className="font-medium">編號：</span>{result.number || '-'}</div>
//                             <div><span className="font-medium">典權胎權：</span>{result.pawnRight || '-'}</div>
//                             <div><span className="font-medium">贌耕權：</span>{result.plowingRight || '-'}</div>
//                             <div><span className="font-medium">現存資料：</span>{result.source || '-'}</div>
//                           </div>

//                           <div className="mt-4 md:mt-0 md:ml-6 flex-none">
//                             <button
//                               className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
//                               onClick={() => {
//                                 setSelectedDetail(result);  // 將該筆資料傳給 DetailPanel
//                                 handleShowDetail(result.number);
//                                 window.scrollTo({ top: 0, behavior: 'smooth' });
//                               }}
//                             >
//                               查看詳情
//                             </button>
//                           </div>
//                         </div>
//                       </div>
//                     ))}
//                   </div>
//                 )}

//                 {/* 分頁 */}
//                 {totalPages > 1 && (
//                   <div className="flex justify-center items-center gap-4 p-4 border-t border-gray-200">
//                     <button onClick={goPrev} disabled={currentPage === 1} className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50">上一頁</button>
//                     <span>第 {currentPage} / {totalPages} 頁</span>
//                     <button onClick={goNext} disabled={currentPage === totalPages} className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50">下一頁</button>
//                   </div>
//                 )}
//               </div>
//             </>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }

import { Suspense } from 'react';
import SearchPageClient from './SearchPageClient';

export default function Page() {
	return (
		<Suspense fallback={<div>Loading…</div>}>
			<SearchPageClient />
		</Suspense>
	);
}
