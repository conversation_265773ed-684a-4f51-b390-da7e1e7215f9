import type { Config } from 'tailwindcss';

const config: Config = {
	content: ['./src/**/*.{js,ts,jsx,tsx,mdx}', './components/**/*.{js,ts,jsx,tsx,mdx}'],
	theme: {
		extend: {
			colors: {
				primary: {
					50: 'var(--color-primary-50)',
					100: 'var(--color-primary-100)',
					200: 'var(--color-primary-200)',
					300: 'var(--color-primary-300)',
					400: 'var(--color-primary-400)',
					500: 'var(--color-primary-500)',
					600: 'var(--color-primary-600)',
					700: 'var(--color-primary-700)',
					800: 'var(--color-primary-800)',
					900: 'var(--color-primary-900)',
					950: 'var(--color-primary-950)',
					DEFAULT: 'var(--color-primary-500)', // 預設主色
				},
				secondary: {
					50: 'var(--color-secondary-50)',
					100: 'var(--color-secondary-100)',
					200: 'var(--color-secondary-200)',
					300: 'var(--color-secondary-300)',
					400: 'var(--color-secondary-400)',
					500: 'var(--color-secondary-500)',
					600: 'var(--color-secondary-600)',
					700: 'var(--color-secondary-700)',
					800: 'var(--color-secondary-800)',
					900: 'var(--color-secondary-900)',
					950: 'var(--color-secondary-950)',
					DEFAULT: 'var(--color-secondary-500)', // 預設次色
				},
				tertiary: {
					50: 'var(--color-tertiary-50)',
					100: 'var(--color-tertiary-100)',
					200: 'var(--color-tertiary-200)',
					300: 'var(--color-tertiary-300)',
					400: 'var(--color-tertiary-400)',
					500: 'var(--color-tertiary-500)',
					600: 'var(--color-tertiary-600)',
					700: 'var(--color-tertiary-700)',
					800: 'var(--color-tertiary-800)',
					900: 'var(--color-tertiary-900)',
					950: 'var(--color-tertiary-950)',
					DEFAULT: 'var(--color-tertiary-500)', // 預設第三色
				},
				quaternary: {
					50: 'var(--color-quaternary-50)',
					100: 'var(--color-quaternary-100)',
					200: 'var(--color-quaternary-200)',
					300: 'var(--color-quaternary-300)',
					400: 'var(--color-quaternary-400)',
					500: 'var(--color-quaternary-500)',
					600: 'var(--color-quaternary-600)',
					700: 'var(--color-quaternary-700)',
					800: 'var(--color-quaternary-800)',
					900: 'var(--color-quaternary-900)',
					950: 'var(--color-quaternary-950)',
					DEFAULT: 'var(--color-quaternary-500)', // 預設第四色
				},
				error: {
					50: 'var(--color-error-50)',
					100: 'var(--color-error-100)',
					200: 'var(--color-error-200)',
					300: 'var(--color-error-300)',
					400: 'var(--color-error-400)',
					500: 'var(--color-error-500)',
					600: 'var(--color-error-600)',
					700: 'var(--color-error-700)',
					800: 'var(--color-error-800)',
					900: 'var(--color-error-900)',
					DEFAULT: 'var(--color-error-500)', // 預設錯誤色
				},
				neutral: {
					white: 'var(--color-neutral-white)',
					50: 'var(--color-neutral-50)',
					100: 'var(--color-neutral-100)',
					200: 'var(--color-neutral-200)',
					300: 'var(--color-neutral-300)',
					400: 'var(--color-neutral-400)',
					500: 'var(--color-neutral-500)',
					600: 'var(--color-neutral-600)',
					700: 'var(--color-neutral-700)',
					800: 'var(--color-neutral-800)',
					900: 'var(--color-neutral-900)',
					black: 'var(--color-neutral-black)',
				},
			},
		},
	},
	plugins: [],
};

export default config;
