// 地圖性能優化工具函數

export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

export interface MapPoint {
  lat: number;
  lng: number;
  [key: string]: any;
}

/**
 * 根據地圖視窗範圍過濾數據點
 * @param data 數據點陣列
 * @param bounds 地圖邊界
 * @param buffer 緩衝區比例 (0.1 = 10% 緩衝區)
 * @returns 過濾後的數據點
 */
export function filterDataByBounds<T extends MapPoint>(
  data: T[],
  bounds: MapBounds,
  buffer: number = 0.1
): T[] {
  const latBuffer = (bounds.north - bounds.south) * buffer;
  const lngBuffer = (bounds.east - bounds.west) * buffer;

  const expandedBounds = {
    north: bounds.north + latBuffer,
    south: bounds.south - latBuffer,
    east: bounds.east + lngBuffer,
    west: bounds.west - lngBuffer,
  };

  return data.filter(point => 
    point.lat >= expandedBounds.south &&
    point.lat <= expandedBounds.north &&
    point.lng >= expandedBounds.west &&
    point.lng <= expandedBounds.east
  );
}

/**
 * 根據縮放層級對數據進行採樣
 * @param data 數據陣列
 * @param zoom 縮放層級
 * @param maxPoints 最大點數
 * @returns 採樣後的數據
 */
export function sampleDataByZoom<T>(
  data: T[],
  zoom: number,
  maxPoints: number = 1000
): T[] {
  if (data.length <= maxPoints) {
    return data;
  }

  // 根據縮放層級調整採樣率
  const zoomFactor = Math.max(0.1, Math.min(1, (zoom - 8) / 10));
  const targetCount = Math.floor(maxPoints * zoomFactor);
  
  if (targetCount >= data.length) {
    return data;
  }

  // 均勻採樣
  const step = data.length / targetCount;
  const sampled: T[] = [];
  
  for (let i = 0; i < data.length; i += step) {
    sampled.push(data[Math.floor(i)]);
  }

  return sampled;
}

/**
 * 創建空間索引網格來加速查詢
 * @param data 數據點陣列
 * @param gridSize 網格大小
 * @returns 空間索引
 */
export function createSpatialIndex<T extends MapPoint>(
  data: T[],
  gridSize: number = 0.01
): Map<string, T[]> {
  const index = new Map<string, T[]>();

  data.forEach(point => {
    const gridX = Math.floor(point.lng / gridSize);
    const gridY = Math.floor(point.lat / gridSize);
    const key = `${gridX},${gridY}`;

    if (!index.has(key)) {
      index.set(key, []);
    }
    index.get(key)!.push(point);
  });

  return index;
}

/**
 * 從空間索引中查詢指定範圍內的數據
 * @param index 空間索引
 * @param bounds 查詢範圍
 * @param gridSize 網格大小
 * @returns 範圍內的數據點
 */
export function queryFromSpatialIndex<T extends MapPoint>(
  index: Map<string, T[]>,
  bounds: MapBounds,
  gridSize: number = 0.01
): T[] {
  const result: T[] = [];
  
  const minGridX = Math.floor(bounds.west / gridSize);
  const maxGridX = Math.floor(bounds.east / gridSize);
  const minGridY = Math.floor(bounds.south / gridSize);
  const maxGridY = Math.floor(bounds.north / gridSize);

  for (let x = minGridX; x <= maxGridX; x++) {
    for (let y = minGridY; y <= maxGridY; y++) {
      const key = `${x},${y}`;
      const gridData = index.get(key);
      if (gridData) {
        // 進一步過濾確保在精確範圍內
        const filtered = gridData.filter(point =>
          point.lat >= bounds.south &&
          point.lat <= bounds.north &&
          point.lng >= bounds.west &&
          point.lng <= bounds.east
        );
        result.push(...filtered);
      }
    }
  }

  return result;
}

/**
 * 防抖函數，用於優化頻繁的地圖事件
 * @param func 要防抖的函數
 * @param delay 延遲時間（毫秒）
 * @returns 防抖後的函數
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * 節流函數，用於限制函數執行頻率
 * @param func 要節流的函數
 * @param limit 時間間隔（毫秒）
 * @returns 節流後的函數
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 計算兩點之間的距離（公里）
 * @param lat1 第一個點的緯度
 * @param lng1 第一個點的經度
 * @param lat2 第二個點的緯度
 * @param lng2 第二個點的經度
 * @returns 距離（公里）
 */
export function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number {
  const R = 6371; // 地球半徑（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * 基於距離的聚合算法
 * @param data 數據點陣列
 * @param maxDistance 最大聚合距離（公里）
 * @param minPoints 最小聚合點數
 * @returns 聚合後的數據
 */
export function clusterByDistance<T extends MapPoint>(
	data: T[],
	maxDistance: number = 1,
	minPoints: number = 2
): Array<{ center: T; items: T[]; count: number }> {
	const clusters: Array<{ center: T; items: T[]; count: number }> = [];
	const processed = new Set<number>();

	data.forEach((point, index) => {
		if (processed.has(index)) return;

		const cluster = { center: point, items: [point], count: 1 };
		processed.add(index);

		// 尋找附近的點
		data.forEach((otherPoint, otherIndex) => {
			if (processed.has(otherIndex) || index === otherIndex) return;

			const distance = calculateDistance(point.lat, point.lng, otherPoint.lat, otherPoint.lng);

			if (distance <= maxDistance) {
				cluster.items.push(otherPoint);
				cluster.count++;
				processed.add(otherIndex);
			}
		});

		// 只有達到最小點數才創建聚合
		if (cluster.count >= minPoints) {
			// 重新計算中心點
			const centerLat = cluster.items.reduce((sum, item) => sum + item.lat, 0) / cluster.items.length;
			const centerLng = cluster.items.reduce((sum, item) => sum + item.lng, 0) / cluster.items.length;
			// 創建新的中心點，保持原始點的其他屬性，但更新座標
			cluster.center = { ...cluster.center, lat: centerLat, lng: centerLng };
			clusters.push(cluster);
		} else {
			// 如果不滿足聚合條件，將點標記為未處理
			cluster.items.forEach((item, _itemIndex) => {
				const originalIndex = data.indexOf(item);
				processed.delete(originalIndex);
			});
		}
	});

	return clusters;
}
