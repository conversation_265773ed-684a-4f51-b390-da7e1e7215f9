'use client'

import { useEffect, useState, useMemo } from 'react';
import { useMap } from 'react-leaflet';
import { SurnameData } from '@/lib/feature-data';
import { filterDataByBounds, sampleDataByZoom, debounce, type MapBounds } from '@/lib/map-performance';
import { getMaxPointsForZoom, getViewportConfig, measurePerformance } from '@/config/map-performance.config';
import {
	createMarkerClusterGroup,
	addMarkersToCluster,
	removeClusterGroupFromMap,
	addClusterGroupToMap,
	createColoredClusterIcon,
	addClusterEventListeners,
	type ClusterEventHandlers,
} from '@/lib/leaflet-clustering';

interface SurnameLayerProps {
	data: SurnameData[];
	visible?: boolean;
}

// 創建聚合圖標
function createSurnameClusterIcon(surname: string, count: number): string {
	return `
    <div style="
      background: rgba(59, 130, 246, 0.9);
      color: white;
      padding: 8px;
      border-radius: 50%;
      font-size: 12px;
      font-weight: bold;
      text-align: center;
      border: 3px solid white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      min-width: 35px;
      min-height: 35px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      line-height: 1;
    ">
      <div style="font-size: 8px; margin-bottom: 1px;">${surname}</div>
      <div>${count}</div>
    </div>
  `;
}

export function SurnameLayer({ data, visible = true }: SurnameLayerProps) {
	const map = useMap();
	const [currentZoom, setCurrentZoom] = useState(10);
	const [mapBounds, setMapBounds] = useState<MapBounds | null>(null);

	// 使用 useMemo 優化數據處理
	const optimizedData = useMemo(() => {
		return measurePerformance('SurnameLayer Data Processing', () => {
			if (!data.length) return [];

			const viewportConfig = getViewportConfig('surname');

			// 1. 先根據視窗範圍過濾（如果有邊界信息）
			const boundsFiltered =
				viewportConfig.enabled && mapBounds ? filterDataByBounds(data, mapBounds, viewportConfig.bufferRatio) : data;

			// 2. 根據縮放層級進行採樣
			const maxPoints = getMaxPointsForZoom(currentZoom, 'surname');
			const sampled = sampleDataByZoom(boundsFiltered, currentZoom, maxPoints);

			return sampled;
		});
	}, [data, mapBounds, currentZoom]);

	// 防抖的邊界更新函數
	const debouncedUpdateBounds = useMemo(() => {
		const viewportConfig = getViewportConfig('surname');
		return debounce((bounds: MapBounds) => {
			setMapBounds(bounds);
		}, viewportConfig.updateDelay);
	}, []);

	// 監聽縮放和移動變化
	useEffect(() => {
		if (!map) return;

		// 設置初始值
		setCurrentZoom(map.getZoom());
		const bounds = map.getBounds();
		setMapBounds({
			north: bounds.getNorth(),
			south: bounds.getSouth(),
			east: bounds.getEast(),
			west: bounds.getWest(),
		});

		// 監聽縮放變化
		const handleZoomEnd = () => {
			const newZoom = map.getZoom();
			setCurrentZoom(newZoom);

			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		// 監聽移動變化
		const handleMoveEnd = () => {
			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		map.on('zoomend', handleZoomEnd);
		map.on('moveend', handleMoveEnd);

		return () => {
			map.off('zoomend', handleZoomEnd);
			map.off('moveend', handleMoveEnd);
		};
	}, [map, debouncedUpdateBounds]);

	useEffect(() => {
		if (!map) return;

		let clusterGroup: any = null;
		let isMounted = true;

		const setupClusterLayer = async () => {
			try {
				// 檢查組件是否仍然掛載
				if (!isMounted) return;

				// 調試信息
				console.warn('SurnameLayer setupClusterLayer:', {
					visible,
					dataLength: data.length,
					optimizedDataLength: optimizedData.length,
					currentZoom,
					hasMapBounds: !!mapBounds,
				});

				// 只有當可見且有數據時才創建標記
				if (visible && optimizedData.length > 0) {
					console.warn('SurnameLayer: Creating cluster with', optimizedData.length, 'items');
					// 性能監控：只在開發環境下輸出
					if (process.env.NODE_ENV === 'development') {
						console.warn(`SurnameLayer: 渲染 ${optimizedData.length}/${data.length} 個標記 (縮放: ${currentZoom})`);
					}

					// 動態導入 Leaflet
					const L = (await import('leaflet')).default;

					// 創建 MarkerClusterGroup
					clusterGroup = await createMarkerClusterGroup({
						maxClusterRadius: 80,
						disableClusteringAtZoom: 15,
						spiderfyOnMaxZoom: true,
						zoomToBoundsOnClick: true,
						showCoverageOnHover: false,
						iconCreateFunction: (cluster: any) => {
							const iconData = createColoredClusterIcon(cluster, '#059669', '姓氏');
							return L.divIcon(iconData);
						},
					});

					// 添加聚合事件監聽器
					const eventHandlers: ClusterEventHandlers = {
						onClusterClick: (event: any) => {
							// 點擊聚合時放大到適當層級
							const cluster = event.layer || event.target;
							if (cluster && cluster.getAllChildMarkers) {
								const childMarkers = cluster.getAllChildMarkers();
								if (childMarkers.length > 0) {
									const group = L.featureGroup(childMarkers);
									map.fitBounds(group.getBounds(), {
										padding: [20, 20],
										maxZoom: 15,
									});
								}
							}
						},
					};

					addClusterEventListeners(clusterGroup, eventHandlers);

					// 創建標記函數
					const createMarkerFunction = (item: SurnameData, L: any) => {
						// 根據縮放層級調整字體大小
						const fontSize = Math.max(10, Math.min(16, currentZoom));

						const surnameIcon = L.divIcon({
							html: `
							<div style="
								background: rgba(5, 150, 105, 0.9);
								color: white;
								padding: 4px 8px;
								border-radius: 4px;
								font-size: ${fontSize}px;
								font-weight: bold;
								text-align: center;
								border: 2px solid white;
								box-shadow: 0 2px 4px rgba(0,0,0,0.3);
								white-space: nowrap;
								min-width: 24px;
								display: flex;
								align-items: center;
								justify-content: center;
							">
								${item.surname}
							</div>
						`,
							className: 'surname-marker',
							iconSize: [Math.max(24, item.surname.length * fontSize * 0.8), 24] as [number, number],
							iconAnchor: [Math.max(12, item.surname.length * fontSize * 0.4), 12] as [number, number],
						});

						const marker = L.marker([item.lat, item.lng], { icon: surnameIcon });

						marker.bindPopup(`
						<div style="font-size: 14px;">
							<strong>姓氏分布</strong><br/>
							<strong>姓氏:</strong> ${item.surname}<br/>
							<strong>地號:</strong> ${item.landNumber}<br/>
							<strong>年份:</strong> ${item.year}
						</div>
					`);

						return marker;
					};

					// 添加標記到聚合組
					console.warn('SurnameLayer: Adding markers to cluster...');
					await addMarkersToCluster(clusterGroup, optimizedData, createMarkerFunction);
					console.warn('SurnameLayer: Markers added to cluster');

					// 添加到地圖
					if (isMounted) {
						console.warn('SurnameLayer: Adding cluster to map...');
						addClusterGroupToMap(map, clusterGroup);
						console.warn('SurnameLayer: Cluster added to map');
					}
				}
			} catch (error) {
				console.error('Error setting up surname cluster layer:', error);
			}
		};

		setupClusterLayer();

		return () => {
			isMounted = false;
			if (clusterGroup) {
				removeClusterGroupFromMap(map, clusterGroup);
			}
		};
	}, [map, optimizedData, visible, currentZoom]);

	return null;
}
