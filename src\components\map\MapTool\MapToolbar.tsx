'use client'

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  ZoomIn,
  ZoomOut,
  Navigation,
  Flame,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MapToolbarProps {
  map: any | null;
  onToolSelect?: (tool: string) => void;
  activeTool?: string | null;
  className?: string;
}

export function MapToolbar({ 
  map, 
  onToolSelect, 
  activeTool, 
  className 
}: MapToolbarProps) {
  const handleToolClick = (tool: string) => {
    onToolSelect?.(tool);
  };

  const handleZoomIn = () => {
    if (map) {
      map.zoomIn();
    }
  };

  const handleZoomOut = () => {
    if (map) {
      map.zoomOut();
    }
  };

  const handleResetView = () => {
    if (map) {
      map.setView([22.5, 120.6], 10);
    }
  };

  const toolGroups = [
		{
			name: '導航工具',
			tools: [
				{ id: 'zoom-in', icon: ZoomIn, label: '放大', onClick: handleZoomIn },
				{ id: 'zoom-out', icon: ZoomOut, label: '縮小', onClick: handleZoomOut },
				{ id: 'reset-view', icon: Navigation, label: '重置視圖', onClick: handleResetView },
			],
		},
		{
			name: '功能工具',
			tools: [
				{ id: 'feature-selector', icon: Settings, label: '功能選擇' },
				{ id: 'heatmap', icon: Flame, label: '熱力圖' }, // 後須有需求再開啟
			],
		},
  ];

  return (
    <Card className={cn("w-fit", "p-0", className)}>
      <CardContent className="p-3">
        <div className="flex flex-wrap gap-2">
          {toolGroups.map((group, groupIndex) => (
            <div key={group.name} className="flex items-center gap-1">
              {groupIndex > 0 && <Separator orientation="vertical" className="h-8 mx-1" />}
              {group.tools.map((tool) => {
                const Icon = tool.icon;
                const isActive = activeTool === tool.id;
                
                return (
                  <Button
                    key={tool.id}
                    variant={isActive ? "default" : "ghost"}
                    size="sm"
                    className={cn(
                      "h-8 w-8 p-0",
                      isActive && "bg-primary text-primary-foreground"
                    )}
                    onClick={() => {
                      if ('onClick' in tool && tool.onClick) {
                        tool.onClick();
                      } else {
                        handleToolClick(tool.id);
                      }
                    }}
                    title={tool.label}
                  >
                    <Icon className="h-4 w-4" />
                  </Button>
                );
              })}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
